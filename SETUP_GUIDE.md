# ThermoFinder 环境配置指南

## 环境信息
- **Conda环境名称**: `thermo_finder`
- **Python版本**: 3.8.13
- **主要依赖**: PyTorch 1.10.1+cu113, TensorFlow 2.6.0, Transformers 4.19.2

## 已安装的核心包

### 机器学习框架
- ✅ PyTorch 1.10.1+cu113 (支持CUDA)
- ✅ TensorFlow 2.6.0 (CPU版本，GPU库有缺失但可运行)
- ✅ Scikit-learn 1.1.1
- ✅ XGBoost 1.6.1
- ✅ LightGBM 3.3.2

### 深度学习和NLP
- ✅ Transformers 4.19.2
- ✅ Hugging Face Hub 0.7.0
- ✅ Tokenizers 0.12.1

### 数据处理
- ✅ NumPy 1.19.5
- ✅ Pandas 1.4.2
- ✅ Matplotlib 3.5.2
- ✅ Biopython 1.79

### 其他工具
- ✅ NLTK 3.9.1
- ✅ OpenPyXL 3.0.10
- ✅ H5PY 3.1.0
- ✅ tqdm 4.64.0

## 激活环境

```bash
conda activate thermo_finder
```

## 测试环境

运行测试脚本验证环境配置：

```bash
python test_environment.py
```

## 项目结构

```
ThermoFinder/
├── Benchmark1.0/          # 基准测试1.0
│   ├── Fused_model_B1.py
│   ├── Single_model_feature_B1.py
│   ├── Single_models_B1.py
│   └── Single_representations_B1.py
├── Benchmark2.0/          # 基准测试2.0
│   ├── Extractor_benchmark_2.py
│   ├── Fused_model_B2.py
│   ├── Single_model_feature_B2.py
│   ├── Single_models_B2.py
│   └── Single_representations_B2.py
├── ThermoSeq_c1.0/        # ThermoSeq分类1.0
│   ├── Extractor_proteome.py
│   └── Fused_model_proteome.py
├── ThermoSeq_r1.0/        # ThermoSeq回归1.0
│   ├── Extractor_accurate.py
│   └── Fused_model_Accurate.py
└── README.md
```

## 数据集

根据README，数据集托管在HuggingFace上：
- 数据集链接: https://huggingface.co/datasets/HanselYu/ThermoSeqNet

## 运行项目

### 1. 基准测试
```bash
cd Benchmark1.0
python Fused_model_B1.py
```

### 2. ThermoSeq分类
```bash
cd ThermoSeq_c1.0
python Fused_model_proteome.py
```

### 3. ThermoSeq回归
```bash
cd ThermoSeq_r1.0
python Fused_model_Accurate.py
```

## 注意事项

1. **数据文件缺失**: 项目代码引用了一些Excel文件（如`Features_protTrans.xlsx`等），这些文件需要从HuggingFace数据集下载。

2. **GPU支持**: 
   - PyTorch GPU支持正常 ✅
   - TensorFlow GPU有库缺失警告，但可以运行 ⚠️

3. **依赖问题**: 
   - 一些老旧的包（如seqvec, tape-proteins）由于setuptools兼容性问题无法安装
   - 已安装的包足以运行项目的核心功能

## 故障排除

### 如果遇到导入错误
```bash
conda activate thermo_finder
pip install --upgrade pip
```

### 如果需要重新安装某个包
```bash
conda activate thermo_finder
pip uninstall <package_name>
pip install <package_name>
```

### 如果需要下载数据集
访问 https://huggingface.co/datasets/HanselYu/ThermoSeqNet 下载所需的数据文件。

## 环境验证

运行以下命令验证环境是否正确配置：

```bash
conda activate thermo_finder
python -c "
import torch, tensorflow as tf, transformers, sklearn
print('Environment OK!')
print(f'PyTorch: {torch.__version__}')
print(f'TensorFlow: {tf.__version__}')
print(f'Transformers: {transformers.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
"
```

## 联系信息

如有问题，请联系项目作者：<EMAIL>
