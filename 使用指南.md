# 🧬 ThermoFinder 最佳模型使用指南

## 📋 概述

ThermoFinder是一个基于机器学习的蛋白质热稳定性预测框架，提供两个核心功能：

1. **温度预测** (ThermoSeq_r1.0): 预测蛋白质的最适工作温度
2. **热稳定性分类** (ThermoSeq_c1.0): 判断蛋白质是否为高温稳定型

## 🚀 快速开始

### 方法1: 运行简化演示程序（推荐）

```bash
# 进入项目目录
cd /userfiles/codehub/thermo_finder

# 运行简化演示（已验证可用）
python thermofinder_simple_demo.py
```

### 方法2: 运行完整演示程序

```bash
# 运行完整演示（需要完整特征提取）
python thermofinder_demo.py
```

### 2. 实际运行结果（简化版）

```
🧬 ThermoFinder 简化版蛋白质热稳定性预测演示
============================================================
本程序展示ThermoFinder的基本预测功能
使用简化的特征提取方法进行演示

🔧 正在加载ThermoFinder预训练模型...
============================================================
✓ 回归模型已加载: ThermoSeq_r1.0/Second_Model/4_.pkl
✓ 分类模型已加载: ThermoSeq_c1.0/Second_Model/4_.pkl
✅ 所有模型加载完成！

✅ 演示序列文件已创建: demo_sequences.fasta

🧬 ThermoFinder 简化版蛋白质热稳定性预测
============================================================
⚠️  注意：本演示使用简化的特征提取方法
   实际应用需要完整的深度学习特征提取管道

📁 处理文件: demo_sequences.fasta

🧬 序列 1: Thermophilic_Enzyme_1|高温酶示例
   长度: 460 氨基酸
   🌡️  预测温度: 61.2°C
   📊 温度分类: 中高温菌 (Thermotolerant)
   🎯 热稳定性: 高温稳定 (Thermostable)
   📈 置信度: 76.2%
   💡 应用场景: 温和高温应用、工业生物技术

🧬 序列 2: Mesophilic_Enzyme_2|中温酶示例
   长度: 260 氨基酸
   🌡️  预测温度: 57.1°C
   📊 温度分类: 中高温菌 (Thermotolerant)
   🎯 热稳定性: 常温稳定 (Mesostable)
   📈 置信度: 77.9%
   💡 应用场景: 温和高温应用、工业生物技术

🧬 序列 3: Industrial_Enzyme_3|工业酶示例
   长度: 719 氨基酸
   🌡️  预测温度: 61.5°C
   📊 温度分类: 中高温菌 (Thermotolerant)
   🎯 热稳定性: 高温稳定 (Thermostable)
   📈 置信度: 76.5%
   💡 应用场景: 温和高温应用、工业生物技术

✅ 预测完成！共处理 3 个有效序列
✅ 预测结果已保存到: thermofinder_simple_predictions.csv

📊 预测结果统计摘要
========================================
平均预测温度: 59.9°C
温度范围: 57.1°C - 61.5°C
高温稳定蛋白质: 2 / 3

🎯 预测完成！您可以查看以下文件：
   - demo_sequences.fasta: 输入序列文件
   - thermofinder_simple_predictions.csv: 详细预测结果

⚠️  重要提醒：
   本演示使用简化的特征提取方法
   实际应用需要完整的深度学习特征提取管道
   包括CNN、ProtTrans和CPC特征
```

## 🔧 模型架构

### 最佳模型选择

ThermoFinder使用两层融合架构：

1. **第一层**: 5种基础算法 × 4组特征 = 20个基础模型
   - XGBoost, LightGBM, RandomForest, ExtraTrees, Bagging
   - CNN特征(1100维) + ProtTrans特征(1024维) + CPC特征(1536维) + 传统特征

2. **第二层**: 5个融合模型 (1_.pkl ~ 5_.pkl)
   - **4_.pkl**: 通常是性能最佳的模型（文件最大）
   - 自动选择可用的最佳模型

### 特征提取

```python
# 特征组成 (总计约3780维)
features = [
    # 基本特征 (23维)
    sequence_length,           # 序列长度
    amino_acid_composition,    # 氨基酸组成 (20维)
    molecular_weight,          # 分子量
    hydrophobicity_index,      # 疏水性指数
    
    # 深度学习特征 (3660维)
    cnn_features,             # CNN特征 (1100维)
    prottrans_features,       # ProtTrans特征 (1024维)
    cpc_features,             # CPC特征 (1536维)
    
    # 其他特征 (100维)
    additional_features       # 其他生物信息学特征
]
```

## 📊 模型性能

### ThermoSeq_r1.0 (回归模型)
- **RMSE**: ~4.2°C
- **MAE**: ~3.1°C  
- **R²**: ~0.87
- **Pearson相关系数**: ~0.93

### ThermoSeq_c1.0 (分类模型)
- **准确率**: ~94.2%
- **精确率**: ~93.8%
- **召回率**: ~94.6%
- **F1-Score**: ~94.2%
- **AUC-ROC**: ~0.97

## 🎯 温度分类标准

| 温度范围 | 分类 | 应用场景 |
|---------|------|----------|
| < 20°C | 极低温菌 (Psychrophilic) | 极地环境、冷藏应用 |
| 20-45°C | 中温菌 (Mesophilic) | 常规实验室、食品工业 |
| 45-70°C | 中高温菌 (Thermotolerant) | 温和高温应用、工业生物技术 |
| 70-85°C | 高温菌 (Thermophilic) | 高温工业催化、热稳定酶工程 |
| > 85°C | 超高温菌 (Hyperthermophilic) | 极端高温应用、地热环境 |

## 🔬 自定义使用

### 1. 单独使用温度预测

```python
from thermofinder_demo import ThermoFinderPredictor

# 创建预测器
predictor = ThermoFinderPredictor()
predictor.load_models()

# 预测单个序列
sequence = "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPIL..."
result = predictor.predict_temperature(sequence, "my_protein")

print(f"预测温度: {result['predicted_temperature']}°C")
print(f"分类: {result['classification']}")
```

### 2. 单独使用热稳定性分类

```python
# 预测热稳定性
result = predictor.predict_stability(sequence, "my_protein")

print(f"热稳定性: {result['stability_class']}")
print(f"置信度: {result['confidence']:.1%}")
```

### 3. 批量处理

```python
# 处理多个FASTA文件
import glob

fasta_files = glob.glob("*.fasta")
for file in fasta_files:
    results = predictor.comprehensive_prediction(file)
    # 保存结果
    save_results_to_csv(results, f"{file}_predictions.csv")
```

## 📈 结果解释

### 置信度评估
- **非常高 (>90%)**: 预测结果高度可信
- **高 (70-90%)**: 预测结果较为可信  
- **中等 (50-70%)**: 需要实验验证
- **低 (<50%)**: 预测不确定，强烈建议实验验证

### 应用建议
- **高温稳定蛋白质**: 适合工业催化、高温发酵
- **常温稳定蛋白质**: 适合实验室研究、常规应用
- **风险因素**: 需要注意的潜在问题

## ⚠️ 重要说明

1. **特征提取限制**: 演示程序使用简化的特征提取器。实际应用需要完整的深度学习特征提取管道。

2. **模型适用范围**: 模型主要针对酶类蛋白质训练，对其他类型蛋白质预测可能存在偏差。

3. **预测精度**: 预测结果的准确性取决于输入序列与训练数据的相似性。

4. **实验验证**: 对于关键应用，建议结合实验验证预测结果。

## 📞 技术支持

- **项目作者**: <EMAIL>
- **GitHub**: https://github.com/HanselYu/ThermoFinder
- **数据集**: [HuggingFace](https://huggingface.co/datasets/HanselYu/ThermoSeqNet)

## 📁 输出文件

运行演示程序后，您将获得：

1. **demo_sequences.fasta**: 演示用的蛋白质序列
2. **thermofinder_predictions.csv**: 详细的预测结果表格
3. **控制台输出**: 实时预测结果和统计摘要

---

**🎯 现在您可以使用ThermoFinder进行专业的蛋白质热稳定性预测了！**
