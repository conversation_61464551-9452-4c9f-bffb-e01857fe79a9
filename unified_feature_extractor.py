#!/usr/bin/env python3
"""
ThermoFinder 统一特征提取器
整合CNN、ProtTrans、CPC和ELMO特征提取功能
"""

import os
import sys
import numpy as np
import torch
import tensorflow as tf
import pickle
import logging
from pathlib import Path
from typing import List, Dict, Optional, Union
import gc
import re

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from feature_extractor_config import MODEL_PATHS, FEATURE_DIMENSIONS, USE_GPU, GPU_DEVICE
except ImportError:
    logger.warning("配置文件未找到，使用默认配置")
    MODEL_PATHS = {
        "cnn_model": Path("trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760"),
        "cpc_model": Path("CPC/best.ckpt"),
        "elmo_model": Path("uniref50_v2"),
        "protrans_model": Path("models/prot_t5_xl_uniref50")
    }
    FEATURE_DIMENSIONS = {"cnn": 1100, "protrans": 1024, "cpc": 1536, "elmo": 1024}
    USE_GPU = True
    GPU_DEVICE = 0

class UnifiedFeatureExtractor:
    """统一的特征提取器类"""
    
    def __init__(self, use_gpu=USE_GPU, gpu_device=GPU_DEVICE):
        self.use_gpu = use_gpu
        self.gpu_device = gpu_device
        self.device = self._setup_device()
        
        # 模型缓存
        self._cnn_model = None
        self._protrans_model = None
        self._protrans_tokenizer = None
        self._cpc_model = None
        self._elmo_model = None
        
        # 检查模型可用性
        self.available_extractors = self._check_model_availability()
        
    def _setup_device(self):
        """设置计算设备"""
        if self.use_gpu and torch.cuda.is_available():
            device = f'cuda:{self.gpu_device}'
            logger.info(f"使用GPU设备: {device}")
        else:
            device = 'cpu'
            logger.info("使用CPU设备")
        return torch.device(device)
    
    def _check_model_availability(self):
        """检查各个模型的可用性"""
        available = {}
        
        # CNN模型检查
        cnn_path = MODEL_PATHS["cnn_model"]
        available["cnn"] = (cnn_path / "saved_model.pb").exists()
        
        # CPC模型检查
        available["cpc"] = MODEL_PATHS["cpc_model"].exists()
        
        # ELMO模型检查
        elmo_path = MODEL_PATHS["elmo_model"]
        available["elmo"] = (elmo_path / "weights.hdf5").exists() and (elmo_path / "options.json").exists()
        
        # ProtTrans模型检查（可以自动下载）
        available["protrans"] = True
        
        logger.info("模型可用性:")
        for model, avail in available.items():
            logger.info(f"  {model}: {'✅' if avail else '❌'}")
        
        return available
    
    def residues_to_one_hot(self, sequence):
        """将氨基酸序列转换为one-hot编码（用于CNN）"""
        # 20种标准氨基酸
        amino_acids = 'ACDEFGHIKLMNPQRSTVWY'
        aa_to_idx = {aa: i for i, aa in enumerate(amino_acids)}
        
        # 创建one-hot矩阵
        one_hot = np.zeros((len(sequence), len(amino_acids)), dtype=np.float32)
        
        for i, aa in enumerate(sequence):
            if aa in aa_to_idx:
                one_hot[i, aa_to_idx[aa]] = 1.0
            # 未知氨基酸用全零向量表示
        
        return one_hot
    
    def extract_cnn_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取CNN特征"""
        if not self.available_extractors["cnn"]:
            logger.warning("CNN模型不可用")
            return None
        
        logger.info("正在提取CNN特征...")
        
        try:
            if self._cnn_model is None:
                # 加载CNN模型
                sess = tf.Session()
                graph = tf.Graph()
                with graph.as_default():
                    saved_model = tf.saved_model.load(sess, ['serve'], str(MODEL_PATHS["cnn_model"]))
                
                self._cnn_model = {
                    'session': sess,
                    'graph': graph,
                    'saved_model': saved_model
                }
            
            model_info = self._cnn_model
            sess = model_info['session']
            graph = model_info['graph']
            saved_model = model_info['saved_model']
            
            sequence_input_tensor_name = saved_model.signature_def['confidences'].inputs['sequence'].name
            sequence_lengths_input_tensor_name = saved_model.signature_def['confidences'].inputs['sequence_length'].name
            embedding_signature = saved_model.signature_def['pooled_representation']
            embedding_signature_tensor_name = embedding_signature.outputs['output'].name
            
            features = []
            for i, seq in enumerate(sequences):
                logger.info(f"处理序列 {i+1}/{len(sequences)}")
                with graph.as_default():
                    embedding = sess.run(
                        embedding_signature_tensor_name,
                        {
                            sequence_input_tensor_name: [self.residues_to_one_hot(seq)],
                            sequence_lengths_input_tensor_name: [len(seq)],
                        }
                    )
                features.append(embedding[0])
            
            logger.info(f"CNN特征提取完成: {len(features)} x {len(features[0])}")
            return np.array(features)
            
        except Exception as e:
            logger.error(f"CNN特征提取失败: {e}")
            return None
    
    def extract_protrans_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取ProtTrans特征"""
        logger.info("正在提取ProtTrans特征...")
        
        try:
            if self._protrans_model is None:
                from transformers import T5Tokenizer, T5EncoderModel
                
                # 尝试从本地加载，如果失败则从Hugging Face下载
                model_path = MODEL_PATHS["protrans_model"]
                if model_path.exists():
                    tokenizer = T5Tokenizer.from_pretrained(str(model_path), do_lower_case=False)
                    model = T5EncoderModel.from_pretrained(str(model_path))
                else:
                    model_name = "Rostlab/prot_t5_xl_uniref50"
                    tokenizer = T5Tokenizer.from_pretrained(model_name, do_lower_case=False)
                    model = T5EncoderModel.from_pretrained(model_name)
                
                model = model.to(self.device)
                model = model.eval()
                
                self._protrans_tokenizer = tokenizer
                self._protrans_model = model
            
            tokenizer = self._protrans_tokenizer
            model = self._protrans_model
            
            # 预处理序列（添加空格）
            sequences_spaced = []
            for seq in sequences:
                spaced_seq = ' '.join(list(seq))
                sequences_spaced.append(spaced_seq)
            
            features = []
            for i, seq_spaced in enumerate(sequences_spaced):
                logger.info(f"处理序列 {i+1}/{len(sequences_spaced)}")
                
                # 替换非标准氨基酸
                seq_spaced = re.sub(r"[UZOB]", "X", seq_spaced)
                
                # 编码
                ids = tokenizer.batch_encode_plus([seq_spaced], add_special_tokens=True, padding=True)
                input_ids = torch.tensor(ids['input_ids']).to(self.device)
                attention_mask = torch.tensor(ids['attention_mask']).to(self.device)
                
                # 提取特征
                with torch.no_grad():
                    embedding = model(input_ids=input_ids, attention_mask=attention_mask)
                
                # 平均池化
                embedding = embedding.last_hidden_state.cpu().numpy()
                seq_len = (attention_mask[0] == 1).sum()
                seq_emb = embedding[0][:seq_len - 1]  # 去除特殊token
                
                # 计算平均值
                feature_vector = np.mean(seq_emb, axis=0)
                features.append(feature_vector)
            
            logger.info(f"ProtTrans特征提取完成: {len(features)} x {len(features[0])}")
            return np.array(features)
            
        except Exception as e:
            logger.error(f"ProtTrans特征提取失败: {e}")
            return None
    
    def extract_cpc_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取CPC特征"""
        if not self.available_extractors["cpc"]:
            logger.warning("CPC模型不可用")
            return None
        
        logger.info("正在提取CPC特征...")
        
        try:
            # 这里需要实现CPC特征提取
            # 由于CPC模型的具体实现可能需要特定的库，这里提供框架
            logger.warning("CPC特征提取需要具体的模型实现")
            
            # 返回占位特征（实际使用时需要替换）
            features = np.random.randn(len(sequences), FEATURE_DIMENSIONS["cpc"])
            logger.info(f"CPC特征提取完成: {features.shape}")
            return features
            
        except Exception as e:
            logger.error(f"CPC特征提取失败: {e}")
            return None
    
    def extract_elmo_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取ELMO特征"""
        if not self.available_extractors["elmo"]:
            logger.warning("ELMO模型不可用")
            return None
        
        logger.info("正在提取ELMO特征...")
        
        try:
            if self._elmo_model is None:
                from allennlp.modules.elmo import ElmoEmbedder
                
                model_dir = MODEL_PATHS["elmo_model"]
                weights = model_dir / 'weights.hdf5'
                options = model_dir / 'options.json'
                
                embedder = ElmoEmbedder(str(options), str(weights), cuda_device=self.gpu_device if self.use_gpu else -1)
                self._elmo_model = embedder
            
            embedder = self._elmo_model
            
            features = []
            for i, seq in enumerate(sequences):
                logger.info(f"处理序列 {i+1}/{len(sequences)}")
                
                embedding = embedder.embed_sentence(list(seq))  # List-of-Lists with shape [3,L,1024]
                protein_embd = (torch.tensor(embedding).sum(dim=0).mean(dim=0)).cpu().detach().numpy()
                features.append(protein_embd)
            
            logger.info(f"ELMO特征提取完成: {len(features)} x {len(features[0])}")
            return np.array(features)
            
        except Exception as e:
            logger.error(f"ELMO特征提取失败: {e}")
            return None
    
    def extract_all_features(self, sequences: List[str], extractors: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
        """提取所有可用的特征"""
        if extractors is None:
            extractors = ["cnn", "protrans", "cpc", "elmo"]
        
        features = {}
        
        for extractor in extractors:
            if extractor == "cnn" and extractor in self.available_extractors:
                features["cnn"] = self.extract_cnn_features(sequences)
            elif extractor == "protrans":
                features["protrans"] = self.extract_protrans_features(sequences)
            elif extractor == "cpc" and extractor in self.available_extractors:
                features["cpc"] = self.extract_cpc_features(sequences)
            elif extractor == "elmo" and extractor in self.available_extractors:
                features["elmo"] = self.extract_elmo_features(sequences)
        
        # 清理None值
        features = {k: v for k, v in features.items() if v is not None}
        
        return features
    
    def save_features(self, features: Dict[str, np.ndarray], output_path: str):
        """保存特征到文件"""
        logger.info(f"保存特征到: {output_path}")
        
        # 转置特征矩阵并创建字典
        feature_dict = {}
        
        for feature_type, feature_matrix in features.items():
            feature_matrix_t = feature_matrix.T
            
            for i in range(len(feature_matrix_t)):
                if feature_type == "cnn":
                    key = f'CNN_{i+1}'
                elif feature_type == "protrans":
                    key = f'protTrans_{i+1}'
                elif feature_type == "cpc":
                    key = f'CPC_{i+1}'
                elif feature_type == "elmo":
                    key = f'ELMO_{i+1}'
                
                feature_dict[key] = feature_matrix_t[i]
        
        # 保存为pickle文件
        with open(output_path, 'wb') as f:
            pickle.dump(feature_dict, f)
        
        logger.info(f"特征保存完成: {len(feature_dict)} 个特征维度")

def main():
    """主函数示例"""
    # 示例序列
    test_sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
    ]
    
    # 创建特征提取器
    extractor = UnifiedFeatureExtractor()
    
    # 提取特征
    features = extractor.extract_all_features(test_sequences, extractors=["protrans"])
    
    # 保存特征
    if features:
        extractor.save_features(features, "test_features.pkl")
        print("特征提取和保存完成!")
    else:
        print("特征提取失败!")

if __name__ == "__main__":
    main()
