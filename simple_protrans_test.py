#!/usr/bin/env python3
"""
简单的ProtTrans特征提取测试
使用正确的模型名称
"""

import torch
import numpy as np
import logging
from transformers import T5Tokenizer, T5EncoderModel
import pickle

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_protrans_simple():
    """简单的ProtTrans测试"""
    logger.info("=== 简单ProtTrans测试 ===")
    
    try:
        # 测试序列
        test_sequences = [
            "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
        ]
        
        # 尝试不同的模型名称
        model_names = [
            "Rostlab/prot_t5_xl_uniref50",
            "prot_t5_xl_uniref50",
            "Rostlab/prot_t5_xl_half_uniref50-enc",
            "Rostlab/prot_t5_base_uniref50"
        ]
        
        for model_name in model_names:
            logger.info(f"尝试加载模型: {model_name}")
            try:
                tokenizer = T5Tokenizer.from_pretrained(model_name, do_lower_case=False)
                model = T5EncoderModel.from_pretrained(model_name)
                
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                model = model.to(device)
                model = model.eval()
                
                logger.info(f"✅ 成功加载模型: {model_name}")
                
                # 提取特征
                features = []
                for i, seq in enumerate(test_sequences):
                    # 预处理序列
                    seq_spaced = ' '.join(list(seq))
                    
                    # 编码
                    ids = tokenizer.batch_encode_plus([seq_spaced], add_special_tokens=True, padding=True)
                    input_ids = torch.tensor(ids['input_ids']).to(device)
                    attention_mask = torch.tensor(ids['attention_mask']).to(device)
                    
                    # 提取特征
                    with torch.no_grad():
                        embedding = model(input_ids=input_ids, attention_mask=attention_mask)
                    
                    # 平均池化
                    embedding = embedding.last_hidden_state.cpu().numpy()
                    seq_len = (attention_mask[0] == 1).sum()
                    seq_emb = embedding[0][:seq_len - 1]  # 去除特殊token
                    
                    # 计算平均值
                    feature_vector = np.mean(seq_emb, axis=0)
                    features.append(feature_vector)
                    
                    logger.info(f"序列 {i+1}: 长度={len(seq)}, 特征维度={len(feature_vector)}")
                
                features = np.array(features)
                logger.info(f"✅ ProtTrans特征提取成功: {features.shape}")
                
                # 保存测试特征
                test_features = {
                    'model_name': model_name,
                    'protTrans_features': features,
                    'sequences': test_sequences,
                    'feature_dim': features.shape[1],
                    'num_sequences': features.shape[0]
                }
                
                with open('working_protrans_features.pkl', 'wb') as f:
                    pickle.dump(test_features, f)
                
                logger.info("✅ 测试特征已保存到 working_protrans_features.pkl")
                return True, model_name
                
            except Exception as e:
                logger.warning(f"❌ 模型 {model_name} 加载失败: {e}")
                continue
        
        logger.error("❌ 所有ProtTrans模型都无法加载")
        return False, None
        
    except Exception as e:
        logger.error(f"❌ ProtTrans测试失败: {e}")
        return False, None

def create_simple_feature_extractor():
    """创建简单的特征提取器"""
    logger.info("=== 创建简单特征提取器 ===")
    
    success, working_model = test_protrans_simple()
    
    if success:
        # 创建简化的特征提取器
        simple_extractor_code = f'''#!/usr/bin/env python3
"""
简化的ThermoFinder特征提取器
使用工作的ProtTrans模型: {working_model}
"""

import torch
import numpy as np
import pickle
import logging
from transformers import T5Tokenizer, T5EncoderModel
from typing import List, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleFeatureExtractor:
    """简化的特征提取器"""
    
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
        self.device = torch.device('cuda' if use_gpu and torch.cuda.is_available() else 'cpu')
        self.model_name = "{working_model}"
        self.tokenizer = None
        self.model = None
        
    def load_model(self):
        """加载ProtTrans模型"""
        if self.tokenizer is None or self.model is None:
            logger.info(f"加载ProtTrans模型: {{self.model_name}}")
            self.tokenizer = T5Tokenizer.from_pretrained(self.model_name, do_lower_case=False)
            self.model = T5EncoderModel.from_pretrained(self.model_name)
            self.model = self.model.to(self.device)
            self.model = self.model.eval()
            logger.info("✅ 模型加载完成")
    
    def extract_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取ProtTrans特征"""
        self.load_model()
        
        logger.info(f"正在提取 {{len(sequences)}} 个序列的特征...")
        features = []
        
        for i, seq in enumerate(sequences):
            if i % 10 == 0:
                logger.info(f"处理序列 {{i+1}}/{{len(sequences)}}")
            
            # 预处理序列
            seq_spaced = ' '.join(list(seq))
            
            # 编码
            ids = self.tokenizer.batch_encode_plus([seq_spaced], add_special_tokens=True, padding=True)
            input_ids = torch.tensor(ids['input_ids']).to(self.device)
            attention_mask = torch.tensor(ids['attention_mask']).to(self.device)
            
            # 提取特征
            with torch.no_grad():
                embedding = self.model(input_ids=input_ids, attention_mask=attention_mask)
            
            # 平均池化
            embedding = embedding.last_hidden_state.cpu().numpy()
            seq_len = (attention_mask[0] == 1).sum()
            seq_emb = embedding[0][:seq_len - 1]  # 去除特殊token
            
            # 计算平均值
            feature_vector = np.mean(seq_emb, axis=0)
            features.append(feature_vector)
        
        features = np.array(features)
        logger.info(f"✅ 特征提取完成: {{features.shape}}")
        return features
    
    def save_features(self, features: np.ndarray, sequences: List[str], output_path: str):
        """保存特征"""
        feature_dict = {{}}
        
        # 转置特征矩阵
        features_t = features.T
        
        for i in range(len(features_t)):
            feature_dict[f'protTrans_{{i+1}}'] = features_t[i]
        
        # 保存
        data = {{
            'features': feature_dict,
            'sequences': sequences,
            'model_name': self.model_name,
            'feature_shape': features.shape
        }}
        
        with open(output_path, 'wb') as f:
            pickle.dump(data, f)
        
        logger.info(f"✅ 特征已保存到: {{output_path}}")

def main():
    """主函数"""
    # 测试序列
    test_sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
    ]
    
    # 创建提取器
    extractor = SimpleFeatureExtractor()
    
    # 提取特征
    features = extractor.extract_features(test_sequences)
    
    if features is not None:
        # 保存特征
        extractor.save_features(features, test_sequences, "simple_extracted_features.pkl")
        print("✅ 特征提取和保存完成!")
    else:
        print("❌ 特征提取失败!")

if __name__ == "__main__":
    main()
'''
        
        with open('simple_feature_extractor_working.py', 'w') as f:
            f.write(simple_extractor_code)
        
        logger.info("✅ 简化特征提取器已创建: simple_feature_extractor_working.py")
        return True
    else:
        logger.error("❌ 无法创建简化特征提取器")
        return False

if __name__ == "__main__":
    create_simple_feature_extractor()
