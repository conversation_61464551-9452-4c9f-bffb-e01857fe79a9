{"architectures": ["T5ForConditionalGeneration"], "d_ff": 16384, "d_kv": 128, "d_model": 1024, "decoder_start_token_id": 0, "dropout_rate": 0.1, "eos_token_id": 1, "feed_forward_proj": "relu", "initializer_factor": 1.0, "is_encoder_decoder": true, "layer_norm_epsilon": 1e-06, "model_type": "t5", "n_positions": 512, "num_decoder_layers": 24, "num_heads": 32, "num_layers": 24, "output_past": true, "pad_token_id": 0, "relative_attention_num_buckets": 32, "use_cache": true, "vocab_size": 128}