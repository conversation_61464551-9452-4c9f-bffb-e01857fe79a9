# ThermoFinder 特征提取器完整配置指南

## 📋 概述

ThermoFinder使用四种主要的特征提取器来分析蛋白质序列：

1. **CNN特征提取器** - 1100维卷积神经网络特征
2. **ProtTrans特征提取器** - 1024维预训练Transformer特征  
3. **CPC特征提取器** - 1536维对比预测编码特征
4. **ELMO特征提取器** - 1024维ELMo嵌入特征

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
# 激活conda环境
conda activate thermo_finder

# 安装缺失的包
pip install sentencepiece
pip install protobuf==3.20.1
```

### 2. 运行配置脚本

```bash
# 运行自动配置脚本
python setup_feature_extractors.py

# 测试环境
python test_feature_extractors.py
```

## 🔧 详细配置步骤

### 1. ProtTrans特征提取器配置

ProtTrans是最稳定和易用的特征提取器，基于Hugging Face Transformers。

#### 自动配置（推荐）

```python
from transformers import T5Tokenizer, T5EncoderModel

# 自动下载模型（需要网络连接）
tokenizer = T5Tokenizer.from_pretrained("Rostlab/prot_t5_xl_uniref50")
model = T5EncoderModel.from_pretrained("Rostlab/prot_t5_xl_uniref50")
```

#### 手动配置

如果网络连接有问题，可以手动下载模型：

1. 访问 https://huggingface.co/Rostlab/prot_t5_xl_uniref50
2. 下载所有模型文件到 `models/prot_t5_xl_uniref50/` 目录
3. 使用本地路径加载模型

#### 使用示例

```python
from improved_feature_extractor import ImprovedFeatureExtractor

extractor = ImprovedFeatureExtractor()
sequences = ["MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"]
features = extractor.extract_protrans_features(sequences)
```

### 2. CNN特征提取器配置

CNN特征提取器需要预训练的TensorFlow模型。

#### 所需文件

```
trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760/
├── saved_model.pb
├── variables/
│   ├── variables.data-00000-of-00001
│   └── variables.index
└── assets/ (可选)
```

#### 获取模型文件

1. **从原始论文获取**: 联系作者 <EMAIL>
2. **从相关项目获取**: 查找类似的蛋白质CNN模型
3. **使用替代模型**: 训练自己的CNN模型

#### 配置步骤

```bash
# 创建模型目录
mkdir -p trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760

# 将模型文件放置到目录中
# saved_model.pb
# variables/
```

### 3. CPC特征提取器配置

CPC (Contrastive Predictive Coding) 特征提取器需要特定的权重文件。

#### 所需文件

```
CPC/
└── best.ckpt
```

#### 获取模型文件

1. **从CPCProt项目获取**: https://github.com/amyxlu/CPCProt
2. **从原始论文获取**: 查找CPC蛋白质模型的相关论文
3. **联系作者**: <EMAIL>

#### 安装CPCProt依赖

```bash
# 安装CPCProt相关依赖
pip install torch-geometric
pip install fair-esm
```

### 4. ELMO特征提取器配置

ELMO特征提取器使用AllenNLP的ELMo模型。

#### 所需文件

```
uniref50_v2/
├── weights.hdf5
└── options.json
```

#### 获取模型文件

1. **从AllenNLP获取**: https://allennlp.org/elmo
2. **从SeqVec项目获取**: https://github.com/mheinzinger/SeqVec
3. **手动下载**: 

```bash
# 创建目录
mkdir -p uniref50_v2

# 下载文件（示例URL，实际URL可能不同）
wget -O uniref50_v2/weights.hdf5 "https://s3-us-west-2.amazonaws.com/allennlp/models/elmo/2x4096_512_2048cnn_2xhighway/elmo_2x4096_512_2048cnn_2xhighway_weights.hdf5"
wget -O uniref50_v2/options.json "https://s3-us-west-2.amazonaws.com/allennlp/models/elmo/2x4096_512_2048cnn_2xhighway/elmo_2x4096_512_2048cnn_2xhighway_options.json"
```

#### 安装AllenNLP

```bash
# 安装AllenNLP（可能需要较长时间）
pip install allennlp==2.10.1
```

## 📁 项目文件结构

配置完成后，项目结构应该如下：

```
ThermoFinder/
├── setup_feature_extractors.py          # 自动配置脚本
├── unified_feature_extractor.py         # 统一特征提取器
├── improved_feature_extractor.py        # 改进的特征提取器
├── test_feature_extractors.py           # 测试脚本
├── feature_extractor_config.py          # 配置文件
├── models/                               # 模型缓存目录
│   └── prot_t5_xl_uniref50/             # ProtTrans模型
├── trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760/  # CNN模型
│   ├── saved_model.pb
│   └── variables/
├── CPC/                                  # CPC模型
│   └── best.ckpt
├── uniref50_v2/                         # ELMO模型
│   ├── weights.hdf5
│   └── options.json
├── ThermoSeq_r1.0/                      # 回归模型
├── ThermoSeq_c1.0/                      # 分类模型
└── README.md
```

## 🧪 测试和验证

### 1. 运行基本测试

```bash
# 测试环境配置
python test_environment.py

# 测试特征提取器
python test_feature_extractors.py
```

### 2. 测试单个特征提取器

```python
# 测试ProtTrans
from improved_feature_extractor import ImprovedFeatureExtractor

extractor = ImprovedFeatureExtractor()
sequences = ["MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"]

# 只提取ProtTrans特征
features = extractor.extract_all_features(sequences, extractors=["protrans"])
print(f"特征形状: {features['protrans'].shape}")
```

### 3. 生成测试特征

```python
# 使用统一接口提取所有可用特征
from unified_feature_extractor import UnifiedFeatureExtractor

extractor = UnifiedFeatureExtractor()
sequences = ["MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"]

# 提取所有可用特征
all_features = extractor.extract_all_features(sequences)

# 保存特征
extractor.save_features(all_features, "test_all_features.pkl")
```

## 🔍 故障排除

### 常见问题

1. **ProtTrans模型下载失败**
   - 检查网络连接
   - 使用代理或VPN
   - 手动下载模型文件

2. **CNN模型不可用**
   - 联系原作者获取模型文件
   - 使用替代的CNN模型
   - 跳过CNN特征，只使用其他特征

3. **内存不足**
   - 减少批处理大小
   - 使用CPU而不是GPU
   - 分批处理序列

4. **CUDA错误**
   - 检查CUDA版本兼容性
   - 使用CPU模式
   - 更新PyTorch版本

### 性能优化

1. **GPU使用**
   ```python
   # 确保使用GPU
   extractor = ImprovedFeatureExtractor(use_gpu=True, gpu_device=0)
   ```

2. **批处理**
   ```python
   # 分批处理大量序列
   batch_size = 10
   for i in range(0, len(sequences), batch_size):
       batch = sequences[i:i+batch_size]
       features = extractor.extract_protrans_features(batch)
   ```

3. **内存管理**
   ```python
   import gc
   import torch
   
   # 定期清理内存
   torch.cuda.empty_cache()
   gc.collect()
   ```

## 📞 支持和联系

- **项目作者**: <EMAIL>
- **GitHub**: https://github.com/HanselYu/ThermoFinder
- **数据集**: https://huggingface.co/datasets/HanselYu/ThermoSeqNet

## 📝 使用示例

### 完整的特征提取流程

```python
#!/usr/bin/env python3
"""
完整的ThermoFinder特征提取示例
"""

from improved_feature_extractor import ImprovedFeatureExtractor
import pickle

def main():
    # 1. 创建特征提取器
    extractor = ImprovedFeatureExtractor(use_gpu=True)
    
    # 2. 加载序列
    sequences = extractor.load_sequences_from_fasta("your_sequences.fasta")
    
    # 3. 提取特征（根据可用模型）
    available_extractors = []
    if extractor.available_extractors["protrans"]:
        available_extractors.append("protrans")
    if extractor.available_extractors["cnn"]:
        available_extractors.append("cnn")
    if extractor.available_extractors["cpc"]:
        available_extractors.append("cpc")
    if extractor.available_extractors["elmo"]:
        available_extractors.append("elmo")
    
    features = extractor.extract_all_features(sequences, extractors=available_extractors)
    
    # 4. 保存特征
    extractor.save_features(features, "extracted_features.pkl")
    
    print(f"✅ 成功提取 {len(sequences)} 个序列的特征")
    for feature_type, feature_matrix in features.items():
        print(f"  {feature_type}: {feature_matrix.shape}")

if __name__ == "__main__":
    main()
```

## 🎯 下一步

1. **配置所需的模型文件**
2. **运行测试脚本验证配置**
3. **使用特征提取器处理您的数据**
4. **集成到ThermoFinder主流程中**

## ✅ 当前状态

### 已完成的工作

1. ✅ **环境配置**: Python 3.8.13 + 所有必需依赖包
2. ✅ **代码框架**: 创建了完整的特征提取器框架
3. ✅ **统一接口**: 开发了统一的特征提取器接口
4. ✅ **错误处理**: 添加了完善的错误处理和日志记录
5. ✅ **测试脚本**: 创建了完整的测试和验证脚本
6. ✅ **配置脚本**: 自动化的模型下载和配置脚本
7. ✅ **文档**: 详细的配置和使用指南

### 需要手动配置的部分

1. ⚠️ **CNN模型**: 需要从原作者获取预训练模型文件
2. ⚠️ **CPC模型**: 需要下载CPCProt权重文件
3. ⚠️ **ELMO模型**: 需要下载UniRef50 ELMo权重
4. ✅ **ProtTrans模型**: 可以自动下载（需要网络连接）

### 立即可用的功能

- ✅ **基础特征提取框架**
- ✅ **ProtTrans特征提取**（网络连接正常时）
- ✅ **序列预处理和格式化**
- ✅ **特征保存和加载**
- ✅ **错误处理和日志记录**

配置完成后，您就可以使用ThermoFinder的完整功能进行蛋白质热稳定性预测了！
