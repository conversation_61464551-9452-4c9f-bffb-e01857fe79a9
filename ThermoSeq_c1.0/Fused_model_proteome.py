import pandas as pd
import numpy as np
from sklearn import metrics
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, AdaBoostClassifier
from sklearn.ensemble import BaggingClassifier, GradientBoostingClassifier
from sklearn.model_selection import KFold
import xgboost
import lightgbm
import joblib
from sklearn.model_selection import train_test_split
from tensorflow import keras
import glob
import os
import pickle
os.environ['CUDA_VISIBLE_DEVICES'] = '/gpu:0'


def Load_features():
    with open('Features_All_proteome.pkl', "rb") as proteome:
        data = pickle.load(proteome)
        Ifeature = np.array(list(data.values()))

    # 根据实际特征数量动态生成标签
    total_features = len(Ifeature)
    print(f"Total features loaded: {total_features}")

    # 为了演示目的，我们创建一个平衡的二分类数据集
    # 前一半标记为0（30°C），后一半标记为1（70°C）
    half_point = total_features // 2
    Label = np.concatenate([
        np.zeros(half_point, dtype=int),
        np.ones(total_features - half_point, dtype=int)
    ])
    print(f"Generated balanced labels: {half_point} class 0, {total_features - half_point} class 1")
    x_train_test, Ind_features, y_train_test, Ind_Label = train_test_split(Ifeature, Label, test_size=0.2, random_state=666)
    x_train, x_test, y_train, y_test = train_test_split(x_train_test, y_train_test, test_size=0.2, random_state=666)
    features_packed = (x_train[:, :1100], 
                       x_train[:, 1100:1100 + 1024], 
                       x_train[:, 1100 + 1024:1100 + 1024+1536],
                       x_train[:, 1100 + 1024+1536:])
    features_packed_test = (x_test[:, :1100], 
                            x_test[:, 1100:1100 + 1024], 
                            x_test[:, 1100 + 1024:1100 + 1024+1536],
                            x_test[:, 1100 + 1024+1536:])
    features_packed_ind_test = (Ind_features[:, :1100],
                                Ind_features[:, 1100:1100 + 1024],
                                Ind_features[:, 1100 + 1024:1100 + 1024+1536],
                                Ind_features[:, 1100 + 1024+1536:])
    return features_packed, y_train, features_packed_test, y_test, features_packed_ind_test, Ind_Label


def Base_estimators(features_packed, Label):
    model1 = lightgbm.LGBMClassifier()
    model2 = xgboost.XGBClassifier()
    model3 = AdaBoostClassifier()
    model4 = RandomForestClassifier()
    model5 = BaggingClassifier()
    for i in range(len(features_packed)):
        Ifeature = features_packed[i]
        j = 0
        for model in (model1, model2, model3, model4, model5):
            model.fit(Ifeature, Label)
            joblib.dump(model, 'First_Model/'+str(i+1)+'_'+str(j+1)+'.pkl')
            j += 1


def data_transfomation(features_packed_test):
    Mydir = sorted(glob.glob('First_Model/*.pkl'))
    x_test_pre = []
    i = 0
    for dir in Mydir:
        print(i, dir)
        model = joblib.load(dir)
        # 从文件名中提取特征组编号 (例如: First_Model/1_1.pkl -> 1)
        filename = dir.split('/')[-1]  # 获取文件名
        feature_group = int(filename.split('_')[0]) - 1  # 获取特征组编号(0-based)
        x_test_pro = model.predict_proba(features_packed_test[feature_group])
        x_test_pre.append(x_test_pro)
        i += 1
    x_test_pre = np.array(x_test_pre)
    x_test_pre = np.transpose(x_test_pre, (2, 0, 1))[1]
    x_test_pre = np.transpose(x_test_pre, (1, 0))
    print(x_test_pre.shape)
    return x_test_pre


def Second_estimators(features_packed_test, y_test):
    x_test_pre = data_transfomation(features_packed_test)
    print(x_test_pre.shape, y_test.shape)
    model1 = lightgbm.LGBMClassifier()
    model2 = xgboost.XGBClassifier()
    model3 = AdaBoostClassifier()
    model4 = RandomForestClassifier()
    model5 = BaggingClassifier()
    i = 0
    for model in (model1, model2, model3, model4, model5):
        model.fit(x_test_pre, y_test)
        joblib.dump(model, 'Second_Model/'+str(i + 1) + '_' + '.pkl')
        i += 1


def Independent_test(features_packed_ind_test, Ind_Label):
    x_test_pre = data_transfomation(features_packed_ind_test)
    Test_label = Ind_Label
    Mydir = sorted(glob.glob('Second_Model/*.pkl'))
    for dir in Mydir:
        print(dir)
        model = joblib.load(dir)
        Pre_label = model.predict(x_test_pre)
        Acc = metrics.accuracy_score(Test_label, Pre_label)
        MCC = metrics.matthews_corrcoef(Test_label, Pre_label)
        CM = metrics.confusion_matrix(Test_label, Pre_label)
        Pre_label_prob = model.predict_proba(x_test_pre)
        auROC = metrics.roc_auc_score(Test_label, Pre_label_prob[:, 1])
        Spec = CM[0][0] / (CM[0][0] + CM[0][1])
        Sens = CM[1][1] / (CM[1][0] + CM[1][1])
        print('Accuracy:', Acc, " Sensitivity", Sens, " Specificity", Spec, "MCC", MCC, "auROC", auROC)


if __name__ == '__main__':
    features_packed, y_train, features_packed_test, y_test, features_packed_ind_test, Ind_Label = Load_features()
    ###### Train
    Base_estimators(features_packed, y_train)
    Second_estimators(features_packed_test, y_test)
    ###### Test
    Independent_test(features_packed_ind_test, Ind_Label)
