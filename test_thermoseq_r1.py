#!/usr/bin/env python3
"""
测试ThermoSeq_r1.0模型
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# 添加路径
sys.path.append('ThermoSeq_r1.0')
os.chdir('ThermoSeq_r1.0')

def test_feature_loading():
    """测试特征加载"""
    print("Testing feature loading...")

    try:
        # 直接导入模块内容
        import importlib.util
        spec = importlib.util.spec_from_file_location("fused_model", "Fused_model_Accurate.py")
        fused_model = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(fused_model)

        features_packed, y_train, features_packed_test, y_test, features_packed_ind_test, Ind_Label = fused_model.Load_features()

        print("✓ Features loaded successfully!")
        print(f"Training set size: {len(y_train)}")
        print(f"Test set size: {len(y_test)}")
        print(f"Independent test set size: {len(Ind_Label)}")
        print(f"Feature dimensions: {[f.shape[1] for f in features_packed]}")

        return True, (features_packed, y_train, features_packed_test, y_test, features_packed_ind_test, Ind_Label, fused_model)

    except Exception as e:
        print(f"✗ Error loading features: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_model_training(data):
    """测试模型训练"""
    print("\nTesting model training...")

    try:
        features_packed, y_train, features_packed_test, y_test, features_packed_ind_test, Ind_Label, fused_model = data

        # 使用小样本进行快速测试
        small_features = tuple(f[:100] for f in features_packed)
        small_labels = y_train[:100]

        print("Training base estimators on small sample...")
        fused_model.Base_estimators(small_features, small_labels)

        print("✓ Model training completed successfully!")
        return True

    except Exception as e:
        print(f"✗ Error in model training: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("ThermoSeq_r1.0 Test")
    print("=" * 30)
    
    # 测试特征加载
    success, data = test_feature_loading()
    
    if success:
        # 测试模型训练
        test_model_training(data)
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
