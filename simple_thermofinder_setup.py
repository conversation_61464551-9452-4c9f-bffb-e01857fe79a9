#!/usr/bin/env python3
"""
简化的ThermoFinder配置脚本
专注于配置ProtTrans特征提取器，这是最稳定和易用的特征提取器
"""

import os
import sys
import numpy as np
import torch
import pickle
import logging
from pathlib import Path
from typing import List, Dict, Optional
import re
import warnings

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 抑制警告
warnings.filterwarnings("ignore")

class SimpleThermoFinderExtractor:
    """简化的ThermoFinder特征提取器，专注于ProtTrans"""
    
    def __init__(self, use_gpu=True, gpu_device=0):
        self.use_gpu = use_gpu
        self.gpu_device = gpu_device
        self.device = self._setup_device()
        
        # 模型缓存
        self._protrans_model = None
        self._protrans_tokenizer = None
        
        logger.info("SimpleThermoFinderExtractor 初始化完成")
        
    def _setup_device(self):
        """设置计算设备"""
        if self.use_gpu and torch.cuda.is_available():
            device = f'cuda:{self.gpu_device}'
            logger.info(f"使用GPU设备: {device}")
        else:
            device = 'cpu'
            logger.info("使用CPU设备")
        return torch.device(device)
    
    def setup_protrans_model(self):
        """配置ProtTrans模型"""
        logger.info("正在配置ProtTrans模型...")
        
        try:
            from transformers import T5Tokenizer, T5EncoderModel
            
            logger.info("正在下载ProtTrans模型（首次运行需要时间）...")
            model_name = "Rostlab/prot_t5_xl_uniref50"
            
            tokenizer = T5Tokenizer.from_pretrained(model_name, do_lower_case=False)
            model = T5EncoderModel.from_pretrained(model_name)
            
            model = model.to(self.device)
            model = model.eval()
            
            self._protrans_tokenizer = tokenizer
            self._protrans_model = model
            
            logger.info("✅ ProtTrans模型配置完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ ProtTrans模型配置失败: {e}")
            return False
    
    def load_sequences_from_fasta(self, fasta_path, max_length=1000):
        """从FASTA文件加载序列"""
        logger.info(f"从 {fasta_path} 加载序列...")
        sequences = []
        
        try:
            with open(fasta_path, 'r') as f:
                current_seq = ""
                for line in f:
                    line = line.strip()
                    if line.startswith('>'):
                        if current_seq:
                            sequences.append(current_seq[:max_length])
                            current_seq = ""
                    else:
                        current_seq += line
                
                # 添加最后一个序列
                if current_seq:
                    sequences.append(current_seq[:max_length])
            
            logger.info(f"成功加载 {len(sequences)} 个序列")
            return sequences
            
        except FileNotFoundError:
            logger.error(f"文件未找到: {fasta_path}")
            return []
        except Exception as e:
            logger.error(f"加载序列时出错: {e}")
            return []
    
    def extract_protrans_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取ProtTrans特征"""
        if self._protrans_model is None:
            logger.error("ProtTrans模型未配置，请先运行 setup_protrans_model()")
            return None
        
        logger.info(f"正在提取 {len(sequences)} 个序列的ProtTrans特征...")
        
        try:
            tokenizer = self._protrans_tokenizer
            model = self._protrans_model
            
            features = []
            for i, seq in enumerate(sequences):
                if i % 10 == 0:
                    logger.info(f"处理进度: {i+1}/{len(sequences)}")
                
                # 预处理序列（添加空格）
                seq_spaced = ' '.join(list(seq))
                
                # 替换非标准氨基酸
                seq_spaced = re.sub(r"[UZOB]", "X", seq_spaced)
                
                # 编码
                ids = tokenizer.batch_encode_plus([seq_spaced], add_special_tokens=True, padding=True)
                input_ids = torch.tensor(ids['input_ids']).to(self.device)
                attention_mask = torch.tensor(ids['attention_mask']).to(self.device)
                
                # 提取特征
                with torch.no_grad():
                    embedding = model(input_ids=input_ids, attention_mask=attention_mask)
                
                # 平均池化
                embedding = embedding.last_hidden_state.cpu().numpy()
                seq_len = (attention_mask[0] == 1).sum()
                seq_emb = embedding[0][:seq_len - 1]  # 去除特殊token
                
                # 计算平均值
                feature_vector = np.mean(seq_emb, axis=0)
                features.append(feature_vector)
                
                # 定期清理GPU内存
                if i % 50 == 0:
                    torch.cuda.empty_cache()
            
            features = np.array(features)
            logger.info(f"✅ ProtTrans特征提取完成: {features.shape}")
            return features
            
        except Exception as e:
            logger.error(f"❌ ProtTrans特征提取失败: {e}")
            return None
    
    def save_features(self, features: np.ndarray, output_path: str, feature_type="protrans"):
        """保存特征到文件"""
        logger.info(f"保存特征到: {output_path}")
        
        try:
            # 转置特征矩阵并创建字典
            feature_dict = {}
            feature_matrix_t = features.T
            
            for i in range(len(feature_matrix_t)):
                if feature_type == "protrans":
                    key = f'protTrans_{i+1}'
                else:
                    key = f'{feature_type}_{i+1}'
                
                feature_dict[key] = feature_matrix_t[i]
            
            # 保存为pickle文件
            with open(output_path, 'wb') as f:
                pickle.dump(feature_dict, f)
            
            logger.info(f"✅ 特征保存完成: {len(feature_dict)} 个特征维度")
            return True
            
        except Exception as e:
            logger.error(f"❌ 特征保存失败: {e}")
            return False
    
    def test_extraction(self):
        """测试特征提取功能"""
        logger.info("=== 测试ProtTrans特征提取 ===")
        
        # 测试序列
        test_sequences = [
            "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
        ]
        
        # 提取特征
        features = self.extract_protrans_features(test_sequences)
        
        if features is not None:
            # 保存测试特征
            success = self.save_features(features, "test_protrans_features.pkl")
            if success:
                logger.info("✅ 测试成功！特征提取器工作正常")
                return True
        
        logger.error("❌ 测试失败")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始配置简化版ThermoFinder")
    
    # 创建特征提取器
    extractor = SimpleThermoFinderExtractor()
    
    # 配置ProtTrans模型
    if not extractor.setup_protrans_model():
        logger.error("模型配置失败，退出")
        return False
    
    # 运行测试
    if not extractor.test_extraction():
        logger.error("测试失败，退出")
        return False
    
    logger.info("🎉 简化版ThermoFinder配置完成！")
    logger.info("现在可以使用ProtTrans特征提取器了")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 配置成功！")
        print("使用方法:")
        print("1. 创建 SimpleThermoFinderExtractor 实例")
        print("2. 调用 setup_protrans_model() 配置模型")
        print("3. 使用 extract_protrans_features() 提取特征")
    else:
        print("\n❌ 配置失败，请检查错误信息")
