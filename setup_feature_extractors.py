#!/usr/bin/env python3
"""
ThermoFinder 特征提取器配置脚本
自动下载和配置CNN、ProtTrans、CPC和ELMO特征提取器所需的模型文件
"""

import os
import sys
import urllib.request
import zipfile
import tarfile
import shutil
from pathlib import Path
import subprocess
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureExtractorSetup:
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.models_dir = self.base_dir / "models"
        self.models_dir.mkdir(exist_ok=True)
        
    def download_file(self, url, filepath, description=""):
        """下载文件"""
        logger.info(f"正在下载 {description}: {url}")
        try:
            urllib.request.urlretrieve(url, filepath)
            logger.info(f"下载完成: {filepath}")
            return True
        except Exception as e:
            logger.error(f"下载失败: {e}")
            return False
    
    def extract_archive(self, archive_path, extract_to):
        """解压文件"""
        logger.info(f"正在解压: {archive_path}")
        try:
            if archive_path.suffix == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
            elif archive_path.suffix in ['.tar', '.gz', '.tgz']:
                with tarfile.open(archive_path, 'r:*') as tar_ref:
                    tar_ref.extractall(extract_to)
            logger.info(f"解压完成: {extract_to}")
            return True
        except Exception as e:
            logger.error(f"解压失败: {e}")
            return False
    
    def setup_protrans_model(self):
        """配置ProtTrans模型 (通过Hugging Face自动下载)"""
        logger.info("=== 配置ProtTrans模型 ===")
        try:
            from transformers import T5Tokenizer, T5EncoderModel
            
            logger.info("正在下载ProtTrans模型...")
            model_name = "Rostlab/prot_t5_xl_uniref50"
            tokenizer = T5Tokenizer.from_pretrained(model_name, do_lower_case=False)
            model = T5EncoderModel.from_pretrained(model_name)
            
            # 保存到本地缓存
            cache_dir = self.models_dir / "prot_t5_xl_uniref50"
            cache_dir.mkdir(exist_ok=True)
            
            tokenizer.save_pretrained(cache_dir)
            model.save_pretrained(cache_dir)
            
            logger.info("✅ ProtTrans模型配置完成")
            return True
        except Exception as e:
            logger.error(f"❌ ProtTrans模型配置失败: {e}")
            return False
    
    def setup_cnn_model(self):
        """配置CNN模型"""
        logger.info("=== 配置CNN模型 ===")
        
        # CNN模型需要从特定源下载，这里提供配置框架
        cnn_model_dir = self.base_dir / "trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760"
        
        if cnn_model_dir.exists():
            logger.info("✅ CNN模型已存在")
            return True
        
        logger.warning("⚠️  CNN模型文件不存在")
        logger.info("请手动下载CNN模型文件到以下目录:")
        logger.info(f"   {cnn_model_dir}")
        logger.info("模型文件应包含TensorFlow SavedModel格式的文件")
        
        # 创建占位目录和说明文件
        cnn_model_dir.mkdir(exist_ok=True)
        readme_file = cnn_model_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write("""# CNN模型配置说明

## 所需文件
请将以下文件放置在此目录中：
- saved_model.pb
- variables/ 目录
- assets/ 目录（如果有）

## 模型来源
此CNN模型来自于蛋白质序列分析的预训练模型。
请联系项目作者获取模型文件：<EMAIL>

## 文件结构
```
trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760/
├── saved_model.pb
├── variables/
│   ├── variables.data-00000-of-00001
│   └── variables.index
└── assets/ (可选)
```
""")
        
        return False
    
    def setup_cpc_model(self):
        """配置CPC模型"""
        logger.info("=== 配置CPC模型 ===")
        
        cpc_dir = self.base_dir / "CPC"
        cpc_dir.mkdir(exist_ok=True)
        
        cpc_model_path = cpc_dir / "best.ckpt"
        
        if cpc_model_path.exists():
            logger.info("✅ CPC模型已存在")
            return True
        
        logger.warning("⚠️  CPC模型文件不存在")
        logger.info("请手动下载CPC模型文件:")
        logger.info(f"   目标路径: {cpc_model_path}")
        
        # 创建说明文件
        readme_file = cpc_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write("""# CPC模型配置说明

## 所需文件
- best.ckpt (CPC预训练权重文件)

## 模型来源
CPC (Contrastive Predictive Coding) 模型用于蛋白质序列表示学习。

可能的下载源：
1. 项目原始仓库
2. 联系作者：<EMAIL>
3. 相关论文的补充材料

## 文件结构
```
CPC/
└── best.ckpt
```
""")
        
        return False
    
    def setup_elmo_model(self):
        """配置ELMO模型"""
        logger.info("=== 配置ELMO模型 ===")
        
        elmo_dir = self.base_dir / "uniref50_v2"
        elmo_dir.mkdir(exist_ok=True)
        
        weights_file = elmo_dir / "weights.hdf5"
        options_file = elmo_dir / "options.json"
        
        if weights_file.exists() and options_file.exists():
            logger.info("✅ ELMO模型已存在")
            return True
        
        logger.warning("⚠️  ELMO模型文件不存在")
        logger.info("请手动下载ELMO模型文件:")
        logger.info(f"   weights.hdf5: {weights_file}")
        logger.info(f"   options.json: {options_file}")
        
        # 创建说明文件
        readme_file = elmo_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write("""# ELMO模型配置说明

## 所需文件
- weights.hdf5 (ELMO权重文件)
- options.json (ELMO配置文件)

## 模型来源
ELMO模型基于UniRef50数据集训练的蛋白质序列嵌入模型。

可能的下载源：
1. AllenNLP官方模型库
2. 生物信息学相关的预训练模型仓库
3. 联系作者：<EMAIL>

## 文件结构
```
uniref50_v2/
├── weights.hdf5
└── options.json
```
""")
        
        return False
    
    def install_missing_packages(self):
        """安装缺失的Python包"""
        logger.info("=== 检查和安装依赖包 ===")
        
        required_packages = [
            "torch",
            "transformers",
            "tensorflow",
            "allennlp",
            "seqvec",
            "tape-proteins"
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"✅ {package} 已安装")
            except ImportError:
                logger.info(f"正在安装 {package}...")
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    logger.info(f"✅ {package} 安装完成")
                except subprocess.CalledProcessError as e:
                    logger.warning(f"⚠️  {package} 安装失败: {e}")
    
    def create_feature_extractor_config(self):
        """创建特征提取器配置文件"""
        config_file = self.base_dir / "feature_extractor_config.py"
        
        config_content = '''"""
ThermoFinder 特征提取器配置文件
"""

import os
from pathlib import Path

# 基础路径配置
BASE_DIR = Path(__file__).parent
MODELS_DIR = BASE_DIR / "models"

# 模型路径配置
MODEL_PATHS = {
    "cnn_model": BASE_DIR / "trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760",
    "cpc_model": BASE_DIR / "CPC" / "best.ckpt",
    "elmo_model": BASE_DIR / "uniref50_v2",
    "protrans_model": MODELS_DIR / "prot_t5_xl_uniref50"
}

# 特征维度配置
FEATURE_DIMENSIONS = {
    "cnn": 1100,
    "protrans": 1024,
    "cpc": 1536,
    "elmo": 1024
}

# GPU配置
USE_GPU = True
GPU_DEVICE = 0

# 检查模型文件是否存在
def check_model_availability():
    """检查所有模型文件是否可用"""
    status = {}
    
    # CNN模型检查
    cnn_path = MODEL_PATHS["cnn_model"]
    status["cnn"] = (cnn_path / "saved_model.pb").exists()
    
    # CPC模型检查
    status["cpc"] = MODEL_PATHS["cpc_model"].exists()
    
    # ELMO模型检查
    elmo_path = MODEL_PATHS["elmo_model"]
    status["elmo"] = (elmo_path / "weights.hdf5").exists() and (elmo_path / "options.json").exists()
    
    # ProtTrans模型检查（可以自动下载）
    status["protrans"] = True
    
    return status

if __name__ == "__main__":
    status = check_model_availability()
    print("模型可用性检查:")
    for model, available in status.items():
        print(f"  {model}: {'✅' if available else '❌'}")
'''
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        logger.info(f"✅ 配置文件已创建: {config_file}")
    
    def run_setup(self):
        """运行完整的设置流程"""
        logger.info("🚀 开始配置ThermoFinder特征提取器")
        
        # 1. 安装依赖包
        self.install_missing_packages()
        
        # 2. 配置各个模型
        protrans_ok = self.setup_protrans_model()
        cnn_ok = self.setup_cnn_model()
        cpc_ok = self.setup_cpc_model()
        elmo_ok = self.setup_elmo_model()
        
        # 3. 创建配置文件
        self.create_feature_extractor_config()
        
        # 4. 总结
        logger.info("\n" + "="*50)
        logger.info("配置完成总结:")
        logger.info(f"  ProtTrans: {'✅' if protrans_ok else '❌'}")
        logger.info(f"  CNN:       {'✅' if cnn_ok else '❌'}")
        logger.info(f"  CPC:       {'✅' if cpc_ok else '❌'}")
        logger.info(f"  ELMO:      {'✅' if elmo_ok else '❌'}")
        
        if not all([cnn_ok, cpc_ok, elmo_ok]):
            logger.warning("\n⚠️  部分模型需要手动下载")
            logger.info("请查看各模型目录中的README.md文件获取详细说明")
        
        logger.info("\n🎉 特征提取器配置完成!")

if __name__ == "__main__":
    setup = FeatureExtractorSetup()
    setup.run_setup()
