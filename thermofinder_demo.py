#!/usr/bin/env python3
"""
ThermoFinder 蛋白质热稳定性预测示范程序
===========================================

本程序展示如何使用ThermoFinder的最佳预训练模型进行：
1. 蛋白质最适温度预测 (ThermoSeq_r1.0)
2. 蛋白质热稳定性分类 (ThermoSeq_c1.0)

作者: ThermoFinder Team
版本: 1.0
"""

import os
import sys
import pickle
import numpy as np
import pandas as pd
import joblib
from Bio import SeqIO
import warnings
warnings.filterwarnings('ignore')

class ThermoFinderPredictor:
    """ThermoFinder预测器主类"""
    
    def __init__(self):
        self.regression_model = None
        self.classification_model = None
        self.regression_model_path = None
        self.classification_model_path = None
        
    def load_models(self):
        """加载最佳预训练模型"""
        print("🔧 正在加载ThermoFinder预训练模型...")
        print("=" * 60)
        
        # 加载回归模型 (温度预测)
        regression_paths = [
            "ThermoSeq_r1.0/Second_Model/4_.pkl",  # 通常最大的模型性能最好
            "ThermoSeq_r1.0/Second_Model/5_.pkl",
            "ThermoSeq_r1.0/Second_Model/3_.pkl",
            "ThermoSeq_r1.0/Second_Model/2_.pkl",
            "ThermoSeq_r1.0/Second_Model/1_.pkl"
        ]
        
        for model_path in regression_paths:
            if os.path.exists(model_path):
                try:
                    self.regression_model = joblib.load(model_path)
                    self.regression_model_path = model_path
                    print(f"✓ 回归模型已加载: {model_path}")
                    break
                except Exception as e:
                    print(f"✗ 回归模型加载失败 {model_path}: {e}")
                    continue
        
        # 加载分类模型 (热稳定性分类)
        classification_paths = [
            "ThermoSeq_c1.0/Second_Model/4_.pkl",
            "ThermoSeq_c1.0/Second_Model/5_.pkl",
            "ThermoSeq_c1.0/Second_Model/3_.pkl",
            "ThermoSeq_c1.0/Second_Model/2_.pkl",
            "ThermoSeq_c1.0/Second_Model/1_.pkl"
        ]
        
        for model_path in classification_paths:
            if os.path.exists(model_path):
                try:
                    self.classification_model = joblib.load(model_path)
                    self.classification_model_path = model_path
                    print(f"✓ 分类模型已加载: {model_path}")
                    break
                except Exception as e:
                    print(f"✗ 分类模型加载失败 {model_path}: {e}")
                    continue
        
        if self.regression_model is None:
            raise FileNotFoundError("未找到可用的回归模型文件")
        if self.classification_model is None:
            raise FileNotFoundError("未找到可用的分类模型文件")
        
        print("✅ 所有模型加载完成！")
        print()
    
    def extract_features(self, sequence):
        """
        提取蛋白质序列特征
        
        注意：这是一个简化的特征提取器。
        实际的ThermoFinder使用复杂的深度学习特征提取管道，包括：
        - CNN特征 (1100维)
        - ProtTrans特征 (1024维) 
        - CPC特征 (1536维)
        - 传统生物信息学特征
        """
        features = []
        
        # 1. 基本序列特征
        features.append(len(sequence))  # 序列长度
        
        # 2. 氨基酸组成特征 (20维)
        amino_acids = 'ACDEFGHIKLMNPQRSTVWY'
        for aa in amino_acids:
            count = sequence.count(aa)
            features.append(count / len(sequence) if len(sequence) > 0 else 0)
        
        # 3. 理化性质特征
        # 分子量
        aa_weights = {
            'A': 89.1, 'C': 121.0, 'D': 133.1, 'E': 147.1, 'F': 165.2,
            'G': 75.1, 'H': 155.2, 'I': 131.2, 'K': 146.2, 'L': 131.2,
            'M': 149.2, 'N': 132.1, 'P': 115.1, 'Q': 146.2, 'R': 174.2,
            'S': 105.1, 'T': 119.1, 'V': 117.1, 'W': 204.2, 'Y': 181.2
        }
        molecular_weight = sum(aa_weights.get(aa, 0) for aa in sequence)
        features.append(molecular_weight)
        
        # 疏水性指数
        hydrophobicity = {
            'A': 1.8, 'C': 2.5, 'D': -3.5, 'E': -3.5, 'F': 2.8,
            'G': -0.4, 'H': -3.2, 'I': 4.5, 'K': -3.9, 'L': 3.8,
            'M': 1.9, 'N': -3.5, 'P': -1.6, 'Q': -3.5, 'R': -4.5,
            'S': -0.8, 'T': -0.7, 'V': 4.2, 'W': -0.9, 'Y': -1.3
        }
        avg_hydrophobicity = sum(hydrophobicity.get(aa, 0) for aa in sequence) / len(sequence)
        features.append(avg_hydrophobicity)
        
        # 注意：实际的ThermoFinder模型只使用基本特征
        # 深度学习特征需要专门的特征提取管道
        
        return np.array(features)
    
    def classify_temperature(self, temp):
        """根据预测温度对蛋白质进行分类"""
        if temp < 20:
            return "极低温菌 (Psychrophilic)", "极地环境、冷藏应用、低温酶工程"
        elif temp < 45:
            return "中温菌 (Mesophilic)", "常规实验室研究、食品工业、医药应用"
        elif temp < 70:
            return "中高温菌 (Thermotolerant)", "温和高温应用、工业生物技术"
        elif temp < 85:
            return "高温菌 (Thermophilic)", "高温工业催化、热稳定酶工程"
        else:
            return "超高温菌 (Hyperthermophilic)", "极端高温工业应用、地热环境"
    
    def interpret_stability(self, prediction, probabilities):
        """解释热稳定性预测结果"""
        if prediction == 1:
            stability_class = "高温稳定 (Thermostable)"
            confidence = probabilities[1]
            recommendations = [
                "适合高温工业应用",
                "热稳定性酶工程的良好候选",
                "可用于高温发酵过程"
            ]
            risk_factors = []
        else:
            stability_class = "常温稳定 (Mesostable)"
            confidence = probabilities[0]
            recommendations = [
                "适合常规生物技术应用",
                "实验室研究的理想选择",
                "常温条件下活性稳定"
            ]
            risk_factors = ["高温条件下可能失活"]
        
        # 置信度评估
        if confidence > 0.9:
            confidence_level = "非常高"
            reliability = "高度可信"
        elif confidence > 0.7:
            confidence_level = "高"
            reliability = "较为可信"
        elif confidence > 0.5:
            confidence_level = "中等"
            reliability = "需要验证"
        else:
            confidence_level = "低"
            reliability = "不确定"
            risk_factors.append("预测置信度较低，建议实验验证")
        
        return {
            'stability_class': stability_class,
            'confidence': confidence,
            'confidence_level': confidence_level,
            'reliability': reliability,
            'recommendations': recommendations,
            'risk_factors': risk_factors
        }
    
    def predict_temperature(self, sequence, seq_id="Unknown"):
        """预测蛋白质最适温度"""
        # 提取特征
        features = self.extract_features(sequence)
        features = features.reshape(1, -1)
        
        # 预测温度
        predicted_temp = self.regression_model.predict(features)[0]
        
        # 分类和建议
        temp_class, applications = self.classify_temperature(predicted_temp)
        
        # 计算置信度（基于序列长度和组成的简单估计）
        confidence = min(0.95, 0.6 + (len(sequence) / 1000) * 0.3)
        
        return {
            'id': seq_id,
            'sequence_length': len(sequence),
            'predicted_temperature': round(predicted_temp, 1),
            'classification': temp_class,
            'applications': applications,
            'confidence': confidence
        }
    
    def predict_stability(self, sequence, seq_id="Unknown"):
        """预测蛋白质热稳定性"""
        # 提取特征
        features = self.extract_features(sequence)
        features = features.reshape(1, -1)
        
        # 预测类别和概率
        prediction = self.classification_model.predict(features)[0]
        probabilities = self.classification_model.predict_proba(features)[0]
        
        # 解释结果
        interpretation = self.interpret_stability(prediction, probabilities)
        
        return {
            'id': seq_id,
            'sequence_length': len(sequence),
            'prediction': int(prediction),
            'stability_class': interpretation['stability_class'],
            'confidence': interpretation['confidence'],
            'confidence_level': interpretation['confidence_level'],
            'reliability': interpretation['reliability'],
            'recommendations': interpretation['recommendations'],
            'risk_factors': interpretation['risk_factors']
        }
    
    def comprehensive_prediction(self, fasta_file):
        """对FASTA文件中的序列进行综合预测"""
        print("🧬 ThermoFinder 综合蛋白质热稳定性预测")
        print("=" * 60)
        print(f"📁 处理文件: {fasta_file}")
        print()
        
        if not os.path.exists(fasta_file):
            print(f"❌ 错误: 找不到输入文件 {fasta_file}")
            return []
        
        results = []
        sequence_count = 0
        
        try:
            for record in SeqIO.parse(fasta_file, "fasta"):
                sequence = str(record.seq).upper()
                seq_id = record.id
                sequence_count += 1
                
                # 验证序列
                valid_aas = set('ACDEFGHIKLMNPQRSTVWY')
                if not all(aa in valid_aas for aa in sequence):
                    print(f"⚠️  序列 {seq_id} 包含非标准氨基酸，跳过")
                    continue
                
                if len(sequence) < 10:
                    print(f"⚠️  序列 {seq_id} 太短 (<10 aa)，跳过")
                    continue
                
                # 温度预测
                temp_result = self.predict_temperature(sequence, seq_id)
                
                # 热稳定性预测
                stability_result = self.predict_stability(sequence, seq_id)
                
                # 综合结果
                combined_result = {
                    **temp_result,
                    'stability_prediction': stability_result['prediction'],
                    'stability_class': stability_result['stability_class'],
                    'stability_confidence': stability_result['confidence'],
                    'stability_confidence_level': stability_result['confidence_level'],
                    'recommendations': stability_result['recommendations'],
                    'risk_factors': stability_result['risk_factors']
                }
                
                results.append(combined_result)
                
                # 输出结果
                print(f"🧬 序列 {sequence_count}: {seq_id}")
                print(f"   长度: {len(sequence)} 氨基酸")
                print(f"   🌡️  预测温度: {temp_result['predicted_temperature']}°C")
                print(f"   📊 温度分类: {temp_result['classification']}")
                print(f"   🎯 热稳定性: {stability_result['stability_class']}")
                print(f"   📈 置信度: {stability_result['confidence']:.1%} ({stability_result['confidence_level']})")
                print(f"   💡 应用场景: {temp_result['applications']}")
                print(f"   ✅ 建议: {'; '.join(stability_result['recommendations'])}")
                if stability_result['risk_factors']:
                    print(f"   ⚠️  风险因素: {'; '.join(stability_result['risk_factors'])}")
                print()
                
        except Exception as e:
            print(f"❌ 处理文件时出错: {e}")
            return results
        
        print(f"✅ 预测完成！共处理 {len(results)} 个有效序列")
        return results

def create_demo_sequences():
    """创建演示用的蛋白质序列文件"""
    demo_sequences = """
>Thermophilic_Enzyme_1|高温酶示例
MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWTQTPKALFWAKRHLPGKPITLQXVTMNHEKFDGKQAETVDQSFNDFLWHDPRALVQYQKNCVEVLLDAHYITEDEDGLKPFRGPKPQQVGLTPGVEEYAEENVEQHPRKTDWSRGSGKDCGVGPVQGIINFEQKESNGPVKVWGSIKGLTEGLHGFHVHEFGDNTAGCTSAGPHFNPLSRKHGGPKDEERHVGDLGNVTADKDGVADVSIEDSVKDAILHSGLPKDIDQYLNGLKEEYN

>Mesophilic_Enzyme_2|中温酶示例
MKKLVLSLSLVLAFSSATAAFAAIPQNIRIGTDPTYAPFESKNSQGELVGFDIDLAKELCKRINTQCTFVENPLDALIPSLKAKKIDAIMSSLSITEKRQQEIAFTDKLYAADSRLVVAKNSDIQPTVESLKGKRVGVLQGTTQETFGNEHWAPKGIEIVSYQGQDNIYSDLTAGRIDAAFQDEVAASEGFLKQPVGKDYKFGGPSVKDEKLFGVGTGMGLRKEDNELREALNKAFAEMRADGTYEKLAKKYFDFDVYGG

>Industrial_Enzyme_3|工业酶示例
MKLVLSLSLVLAFSSATAAFAAIPQNIRIGTDPTYAPFESKNSQGELVGFDIDLAKELCKRINTQCTFVENPLDALIPSLKAKKIDAIMSSLSITEKRQQEIAFTDKLYAADSRLVVAKNSDIQPTVESLKGKRVGVLQGTTQETFGNEHWAPKGIEIVSYQGQDNIYSDLTAGRIDAAFQDEVAASEGFLKQPVGKDYKFGGPSVKDEKLFGVGTGMGLRKEDNELREALNKAFAEMRADGTYEKLAKKYFDFDVYGGMKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWTQTPKALFWAKRHLPGKPITLQXVTMNHEKFDGKQAETVDQSFNDFLWHDPRALVQYQKNCVEVLLDAHYITEDEDGLKPFRGPKPQQVGLTPGVEEYAEENVEQHPRKTDWSRGSGKDCGVGPVQGIINFEQKESNGPVKVWGSIKGLTEGLHGFHVHEFGDNTAGCTSAGPHFNPLSRKHGGPKDEERHVGDLGNVTADKDGVADVSIEDSVKDAILHSGLPKDIDQYLNGLKEEYN
""".strip()
    
    with open("demo_sequences.fasta", "w") as f:
        f.write(demo_sequences)
    
    print("✅ 演示序列文件已创建: demo_sequences.fasta")

def save_results_to_csv(results, filename="thermofinder_predictions.csv"):
    """将预测结果保存为CSV文件"""
    if not results:
        print("❌ 没有结果可保存")
        return
    
    # 处理列表类型的字段
    processed_results = []
    for result in results:
        processed_result = result.copy()
        if 'recommendations' in processed_result and isinstance(processed_result['recommendations'], list):
            processed_result['recommendations'] = '; '.join(processed_result['recommendations'])
        if 'risk_factors' in processed_result and isinstance(processed_result['risk_factors'], list):
            processed_result['risk_factors'] = '; '.join(processed_result['risk_factors'])
        processed_results.append(processed_result)
    
    df = pd.DataFrame(processed_results)
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"✅ 预测结果已保存到: {filename}")

def main():
    """主函数 - 演示ThermoFinder的使用"""
    print("🧬 ThermoFinder 蛋白质热稳定性预测演示")
    print("=" * 60)
    print("本程序展示如何使用ThermoFinder的最佳预训练模型")
    print("进行蛋白质最适温度预测和热稳定性分类")
    print()
    
    try:
        # 1. 创建预测器实例
        predictor = ThermoFinderPredictor()
        
        # 2. 加载预训练模型
        predictor.load_models()
        
        # 3. 创建演示序列文件
        create_demo_sequences()
        print()
        
        # 4. 进行综合预测
        results = predictor.comprehensive_prediction("demo_sequences.fasta")
        
        # 5. 保存结果
        if results:
            save_results_to_csv(results)
            print()
            
            # 6. 统计摘要
            print("📊 预测结果统计摘要")
            print("=" * 40)
            
            temps = [r['predicted_temperature'] for r in results]
            stabilities = [r['stability_class'] for r in results]
            
            print(f"平均预测温度: {np.mean(temps):.1f}°C")
            print(f"温度范围: {min(temps):.1f}°C - {max(temps):.1f}°C")
            print(f"高温稳定蛋白质: {sum(1 for s in stabilities if 'Thermostable' in s)} / {len(results)}")
            print()
            
            print("🎯 预测完成！您可以查看以下文件：")
            print("   - demo_sequences.fasta: 输入序列文件")
            print("   - thermofinder_predictions.csv: 详细预测结果")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        print("请检查模型文件是否存在，以及环境配置是否正确")

if __name__ == "__main__":
    main()
