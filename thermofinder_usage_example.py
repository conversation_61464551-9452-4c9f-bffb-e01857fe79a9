#!/usr/bin/env python3
"""
ThermoFinder 使用示例
展示如何使用完整的特征提取器进行蛋白质热稳定性预测
"""

import os
import sys
import numpy as np
import pickle
import logging
from pathlib import Path
from complete_feature_extractor import CompleteThermoFinderExtractor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_demo_sequences():
    """创建演示用的蛋白质序列"""
    demo_sequences = [
        # 嗜热蛋白序列示例
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ",
        # 常温蛋白序列示例
        "MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL",
        "MNIFEMLRIDEGLRLKIYKDTEGYYTIGIGHLLTKSPSLNAAKSELDKAIGRNTNGVITKDEAEKLFNQDVDAAVRGILRNAKLKPVYDSLDAVRRAALINMVFQMGETGVAGFTNSLRMLQQKRWDEAAVNLAKSRWYNQTPNRAKRVITTFRTGTWDAYKNL"
    ]
    
    return demo_sequences

def save_demo_fasta(sequences, filename="demo_sequences.fasta"):
    """保存演示序列为FASTA格式"""
    with open(filename, 'w') as f:
        for i, seq in enumerate(sequences):
            f.write(f">demo_protein_{i+1}\n")
            f.write(f"{seq}\n")
    
    logger.info(f"演示序列已保存到: {filename}")
    return filename

def extract_features_example():
    """特征提取示例"""
    logger.info("=== ThermoFinder 特征提取示例 ===")
    
    # 1. 创建演示序列
    sequences = create_demo_sequences()
    fasta_file = save_demo_fasta(sequences)
    
    # 2. 创建特征提取器
    logger.info("创建特征提取器...")
    extractor = CompleteThermoFinderExtractor(
        use_gpu=True,
        gpu_device=0,
        offline_mode=True  # 使用离线模式避免网络问题
    )
    
    # 3. 从FASTA文件加载序列
    logger.info("从FASTA文件加载序列...")
    loaded_sequences = extractor.load_sequences_from_fasta(fasta_file)
    logger.info(f"加载了 {len(loaded_sequences)} 个序列")
    
    # 4. 提取所有可用特征
    logger.info("提取特征...")
    features = extractor.extract_all_features(
        loaded_sequences, 
        extractors=["protrans", "cnn", "cpc", "elmo"]
    )
    
    # 5. 保存特征
    output_file = "demo_features.pkl"
    logger.info(f"保存特征到: {output_file}")
    success = extractor.save_features(features, output_file)
    
    if success:
        logger.info("✅ 特征提取和保存完成")
        
        # 显示特征信息
        logger.info("提取的特征信息:")
        for feature_type, feature_matrix in features.items():
            logger.info(f"  {feature_type}: {feature_matrix.shape} (序列数 x 特征维度)")
        
        return output_file, features
    else:
        logger.error("❌ 特征保存失败")
        return None, None

def load_and_analyze_features(feature_file):
    """加载和分析特征"""
    logger.info(f"=== 分析特征文件: {feature_file} ===")
    
    try:
        with open(feature_file, 'rb') as f:
            feature_dict = pickle.load(f)
        
        logger.info(f"特征文件包含 {len(feature_dict)} 个特征维度")
        
        # 分析特征类型
        feature_types = {}
        for key in feature_dict.keys():
            if key.startswith('protTrans_'):
                feature_types.setdefault('protTrans', []).append(key)
            elif key.startswith('CNN_'):
                feature_types.setdefault('CNN', []).append(key)
            elif key.startswith('CPC_'):
                feature_types.setdefault('CPC', []).append(key)
            elif key.startswith('ELMO_'):
                feature_types.setdefault('ELMO', []).append(key)
        
        logger.info("特征类型统计:")
        for ftype, keys in feature_types.items():
            logger.info(f"  {ftype}: {len(keys)} 维")
        
        # 显示特征统计信息
        sample_key = list(feature_dict.keys())[0]
        num_sequences = len(feature_dict[sample_key])
        logger.info(f"序列数量: {num_sequences}")
        
        return feature_dict
        
    except Exception as e:
        logger.error(f"加载特征文件失败: {e}")
        return None

def predict_thermostability_demo(features):
    """演示热稳定性预测（简化版）"""
    logger.info("=== 热稳定性预测演示 ===")
    
    if not features:
        logger.error("没有可用的特征进行预测")
        return
    
    # 这里应该加载训练好的模型进行预测
    # 由于没有实际的模型文件，我们创建一个简单的演示
    
    logger.info("注意: 这是一个简化的演示，实际预测需要训练好的模型")
    
    # 获取第一种特征类型进行演示
    feature_type = list(features.keys())[0]
    feature_matrix = features[feature_type]
    
    logger.info(f"使用 {feature_type} 特征进行演示预测")
    logger.info(f"特征矩阵形状: {feature_matrix.shape}")
    
    # 简单的演示预测（基于特征的简单统计）
    predictions = []
    for i in range(feature_matrix.shape[0]):
        # 简单的启发式预测
        feature_sum = np.sum(feature_matrix[i])
        feature_mean = np.mean(feature_matrix[i])
        
        # 基于特征统计的简单分类
        if feature_mean > 0.1:
            prediction = "嗜热蛋白 (Thermophilic)"
            confidence = min(0.9, abs(feature_mean) * 10)
        else:
            prediction = "常温蛋白 (Mesophilic)"
            confidence = min(0.9, abs(feature_mean) * 10)
        
        predictions.append((prediction, confidence))
        logger.info(f"序列 {i+1}: {prediction} (置信度: {confidence:.2f})")
    
    return predictions

def main():
    """主函数"""
    logger.info("🚀 ThermoFinder 使用示例开始")
    
    try:
        # 1. 特征提取示例
        feature_file, features = extract_features_example()
        
        if feature_file and features:
            # 2. 特征分析
            feature_dict = load_and_analyze_features(feature_file)
            
            # 3. 预测演示
            predictions = predict_thermostability_demo(features)
            
            logger.info("🎉 ThermoFinder 使用示例完成")
            logger.info("\n总结:")
            logger.info("1. ✅ 成功提取了蛋白质序列特征")
            logger.info("2. ✅ 特征已保存为ThermoFinder兼容格式")
            logger.info("3. ✅ 演示了简单的热稳定性预测")
            logger.info("\n下一步:")
            logger.info("- 配置真实的模型文件以获得更准确的特征")
            logger.info("- 使用训练好的分类器进行实际预测")
            logger.info("- 集成到完整的ThermoFinder流程中")
            
        else:
            logger.error("特征提取失败，无法继续演示")
            
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 演示成功完成！")
    else:
        print("\n❌ 演示失败，请检查错误信息")
