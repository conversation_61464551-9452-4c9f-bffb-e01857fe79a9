#!/usr/bin/env python3
"""
ThermoFinder 特征提取器测试脚本
测试CNN、ProtTrans、CPC和ELMO特征提取器的功能
"""

import os
import sys
import numpy as np
import torch
import pickle
import logging
from pathlib import Path
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment():
    """测试环境配置"""
    logger.info("=== 环境测试 ===")
    
    # 测试基本包
    try:
        import numpy as np
        logger.info(f"✅ NumPy {np.__version__}")
    except ImportError:
        logger.error("❌ NumPy 未安装")
        return False
    
    try:
        import torch
        logger.info(f"✅ PyTorch {torch.__version__}")
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA 可用: {torch.cuda.get_device_name()}")
        else:
            logger.info("⚠️  CUDA 不可用，将使用CPU")
    except ImportError:
        logger.error("❌ PyTorch 未安装")
        return False
    
    try:
        from transformers import T5Tokenizer, T5EncoderModel
        logger.info("✅ Transformers 可用")
    except ImportError:
        logger.error("❌ Transformers 未安装")
        return False
    
    # 测试TensorFlow
    try:
        import tensorflow as tf
        logger.info(f"✅ TensorFlow {tf.__version__}")
    except ImportError:
        logger.error("❌ TensorFlow 未安装")
        return False
    
    return True

def test_protrans_extractor():
    """测试ProtTrans特征提取器"""
    logger.info("=== 测试ProtTrans特征提取器 ===")
    
    try:
        from transformers import T5Tokenizer, T5EncoderModel
        import torch
        
        # 测试序列
        test_sequences = [
            "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
        ]
        
        logger.info("加载ProtTrans模型...")
        start_time = time.time()
        
        model_name = "Rostlab/prot_t5_xl_uniref50"
        tokenizer = T5Tokenizer.from_pretrained(model_name, do_lower_case=False)
        model = T5EncoderModel.from_pretrained(model_name)
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model = model.eval()
        
        load_time = time.time() - start_time
        logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
        
        # 提取特征
        logger.info("提取特征...")
        features = []
        
        for i, seq in enumerate(test_sequences):
            # 预处理序列
            seq_spaced = ' '.join(list(seq))
            
            # 编码
            ids = tokenizer.batch_encode_plus([seq_spaced], add_special_tokens=True, padding=True)
            input_ids = torch.tensor(ids['input_ids']).to(device)
            attention_mask = torch.tensor(ids['attention_mask']).to(device)
            
            # 提取特征
            with torch.no_grad():
                embedding = model(input_ids=input_ids, attention_mask=attention_mask)
            
            # 平均池化
            embedding = embedding.last_hidden_state.cpu().numpy()
            seq_len = (attention_mask[0] == 1).sum()
            seq_emb = embedding[0][:seq_len - 1]  # 去除特殊token
            
            # 计算平均值
            feature_vector = np.mean(seq_emb, axis=0)
            features.append(feature_vector)
            
            logger.info(f"序列 {i+1}: 长度={len(seq)}, 特征维度={len(feature_vector)}")
        
        features = np.array(features)
        logger.info(f"✅ ProtTrans特征提取成功: {features.shape}")
        
        # 保存测试特征
        test_features = {
            'protTrans_features': features,
            'sequences': test_sequences,
            'feature_dim': features.shape[1],
            'num_sequences': features.shape[0]
        }
        
        with open('test_protrans_features.pkl', 'wb') as f:
            pickle.dump(test_features, f)
        
        logger.info("✅ 测试特征已保存到 test_protrans_features.pkl")
        return True
        
    except Exception as e:
        logger.error(f"❌ ProtTrans测试失败: {e}")
        return False

def test_model_availability():
    """测试模型文件可用性"""
    logger.info("=== 检查模型文件可用性 ===")
    
    models = {
        "CNN模型": "trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760/saved_model.pb",
        "CPC模型": "CPC/best.ckpt",
        "ELMO权重": "uniref50_v2/weights.hdf5",
        "ELMO配置": "uniref50_v2/options.json"
    }
    
    available_models = {}
    
    for model_name, model_path in models.items():
        if Path(model_path).exists():
            logger.info(f"✅ {model_name}: 可用")
            available_models[model_name] = True
        else:
            logger.info(f"❌ {model_name}: 不可用 ({model_path})")
            available_models[model_name] = False
    
    return available_models

def test_unified_extractor():
    """测试统一特征提取器"""
    logger.info("=== 测试统一特征提取器 ===")
    
    try:
        from unified_feature_extractor import UnifiedFeatureExtractor
        
        # 创建提取器
        extractor = UnifiedFeatureExtractor()
        
        # 测试序列
        test_sequences = [
            "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
        ]
        
        # 提取特征
        features = extractor.extract_all_features(test_sequences, extractors=["protrans"])
        
        if features:
            logger.info(f"✅ 统一特征提取器测试成功")
            for feature_type, feature_matrix in features.items():
                logger.info(f"  {feature_type}: {feature_matrix.shape}")
            
            # 保存特征
            extractor.save_features(features, "test_unified_features.pkl")
            logger.info("✅ 特征已保存到 test_unified_features.pkl")
            return True
        else:
            logger.error("❌ 统一特征提取器返回空结果")
            return False
            
    except Exception as e:
        logger.error(f"❌ 统一特征提取器测试失败: {e}")
        return False

def test_improved_extractor():
    """测试改进的特征提取器"""
    logger.info("=== 测试改进的特征提取器 ===")
    
    try:
        from improved_feature_extractor import ImprovedFeatureExtractor
        
        # 创建提取器
        extractor = ImprovedFeatureExtractor()
        
        # 测试序列
        test_sequences = [
            "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
        ]
        
        # 提取特征
        features = extractor.extract_all_features(test_sequences, extractors=["protrans"])
        
        if features:
            logger.info(f"✅ 改进特征提取器测试成功")
            for feature_type, feature_matrix in features.items():
                logger.info(f"  {feature_type}: {feature_matrix.shape}")
            
            # 保存特征
            extractor.save_features(features, "test_improved_features.pkl")
            logger.info("✅ 特征已保存到 test_improved_features.pkl")
            return True
        else:
            logger.error("❌ 改进特征提取器返回空结果")
            return False
            
    except Exception as e:
        logger.error(f"❌ 改进特征提取器测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("🚀 开始ThermoFinder特征提取器测试")
    
    results = {}
    
    # 1. 环境测试
    results['environment'] = test_environment()
    
    # 2. 模型可用性测试
    results['model_availability'] = test_model_availability()
    
    # 3. ProtTrans测试
    results['protrans'] = test_protrans_extractor()
    
    # 4. 统一特征提取器测试
    results['unified_extractor'] = test_unified_extractor()
    
    # 5. 改进特征提取器测试
    results['improved_extractor'] = test_improved_extractor()
    
    # 总结
    logger.info("\n" + "="*50)
    logger.info("测试结果总结:")
    for test_name, result in results.items():
        if isinstance(result, dict):
            logger.info(f"  {test_name}:")
            for sub_test, sub_result in result.items():
                status = "✅" if sub_result else "❌"
                logger.info(f"    {sub_test}: {status}")
        else:
            status = "✅" if result else "❌"
            logger.info(f"  {test_name}: {status}")
    
    # 检查是否有任何测试通过
    passed_tests = []
    if results.get('environment'):
        passed_tests.append('环境配置')
    if results.get('protrans'):
        passed_tests.append('ProtTrans特征提取')
    if results.get('unified_extractor'):
        passed_tests.append('统一特征提取器')
    if results.get('improved_extractor'):
        passed_tests.append('改进特征提取器')
    
    if passed_tests:
        logger.info(f"\n🎉 以下测试通过: {', '.join(passed_tests)}")
        logger.info("ThermoFinder特征提取器基本功能正常!")
    else:
        logger.info("\n⚠️  所有测试都失败了，请检查环境配置")
    
    return results

if __name__ == "__main__":
    run_all_tests()
