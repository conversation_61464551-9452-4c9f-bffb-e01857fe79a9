#!/usr/bin/env python3
"""
测试ThermoFinder环境配置
"""

import sys
import warnings
warnings.filterwarnings('ignore')

def test_imports():
    """测试所有必要的包是否能正常导入"""
    print("Testing package imports...")
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy: {e}")
        return False
    
    try:
        import pandas as pd
        print(f"✓ Pandas {pd.__version__}")
    except ImportError as e:
        print(f"✗ Pandas: {e}")
        return False
    
    try:
        import sklearn
        print(f"✓ Scikit-learn {sklearn.__version__}")
    except ImportError as e:
        print(f"✗ Scikit-learn: {e}")
        return False
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch: {e}")
        return False
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow {tf.__version__}")
    except ImportError as e:
        print(f"✗ TensorFlow: {e}")
        return False
    
    try:
        import transformers
        print(f"✓ Transformers {transformers.__version__}")
    except ImportError as e:
        print(f"✗ Transformers: {e}")
        return False
    
    try:
        import Bio
        print(f"✓ Biopython {Bio.__version__}")
    except ImportError as e:
        print(f"✗ Biopython: {e}")
        return False
    
    try:
        import lightgbm as lgb
        print(f"✓ LightGBM {lgb.__version__}")
    except ImportError as e:
        print(f"✗ LightGBM: {e}")
        return False
    
    try:
        import xgboost as xgb
        print(f"✓ XGBoost {xgb.__version__}")
    except ImportError as e:
        print(f"✗ XGBoost: {e}")
        return False
    
    try:
        import matplotlib
        print(f"✓ Matplotlib {matplotlib.__version__}")
    except ImportError as e:
        print(f"✗ Matplotlib: {e}")
        return False
    
    return True

def test_gpu():
    """测试GPU是否可用"""
    print("\nTesting GPU availability...")
    
    # 测试PyTorch GPU
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ PyTorch CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠ PyTorch CUDA not available")
    except Exception as e:
        print(f"✗ PyTorch GPU test failed: {e}")
    
    # 测试TensorFlow GPU
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✓ TensorFlow GPU available: {len(gpus)} device(s)")
        else:
            print("⚠ TensorFlow GPU not available")
    except Exception as e:
        print(f"✗ TensorFlow GPU test failed: {e}")

def test_basic_functionality():
    """测试基本功能"""
    print("\nTesting basic functionality...")
    
    try:
        import numpy as np
        import pandas as pd
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        
        # 创建测试数据
        X = np.random.rand(100, 10)
        y = np.random.randint(0, 2, 100)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 训练模型
        rf = RandomForestClassifier(n_estimators=10, random_state=42)
        rf.fit(X_train, y_train)
        
        # 预测
        predictions = rf.predict(X_test)
        
        print("✓ Basic ML pipeline works")
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

def main():
    """主函数"""
    print("ThermoFinder Environment Test")
    print("=" * 40)
    
    # 测试导入
    imports_ok = test_imports()
    
    # 测试GPU
    test_gpu()
    
    # 测试基本功能
    functionality_ok = test_basic_functionality()
    
    print("\n" + "=" * 40)
    if imports_ok and functionality_ok:
        print("✓ Environment setup successful!")
        print("You can now run ThermoFinder scripts.")
    else:
        print("✗ Environment setup incomplete.")
        print("Please check the error messages above.")
    
    return imports_ok and functionality_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
