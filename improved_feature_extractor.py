#!/usr/bin/env python3
"""
改进的ThermoFinder特征提取器
修复了原始代码中的问题并添加了错误处理
"""

import os
import sys
import numpy as np
import torch
import pickle
import logging
from pathlib import Path
from typing import List, Dict, Optional, Union
import gc
import re
import warnings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 抑制警告
warnings.filterwarnings("ignore")

# 氨基酸词汇表
AMINO_ACID_VOCABULARY = [
    'A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R',
    'S', 'T', 'V', 'W', 'Y'
]

_PFAM_GAP_CHARACTER = '.'

class ImprovedFeatureExtractor:
    """改进的特征提取器类"""
    
    def __init__(self, use_gpu=True, gpu_device=0):
        self.use_gpu = use_gpu
        self.gpu_device = gpu_device
        self.device = self._setup_device()
        
        # 模型缓存
        self._cnn_model = None
        self._protrans_model = None
        self._protrans_tokenizer = None
        self._cpc_model = None
        self._elmo_model = None
        
        # 检查模型可用性
        self.available_extractors = self._check_model_availability()
        
    def _setup_device(self):
        """设置计算设备"""
        if self.use_gpu and torch.cuda.is_available():
            device = f'cuda:{self.gpu_device}'
            logger.info(f"使用GPU设备: {device}")
        else:
            device = 'cpu'
            logger.info("使用CPU设备")
        return torch.device(device)
    
    def _check_model_availability(self):
        """检查各个模型的可用性"""
        available = {}
        
        # CNN模型检查
        cnn_path = Path("trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760")
        available["cnn"] = (cnn_path / "saved_model.pb").exists()
        
        # CPC模型检查
        cpc_path = Path("CPC/best.ckpt")
        available["cpc"] = cpc_path.exists()
        
        # ELMO模型检查
        elmo_path = Path("uniref50_v2")
        available["elmo"] = (elmo_path / "weights.hdf5").exists() and (elmo_path / "options.json").exists()
        
        # ProtTrans模型检查（可以自动下载）
        available["protrans"] = True
        
        logger.info("模型可用性:")
        for model, avail in available.items():
            logger.info(f"  {model}: {'✅' if avail else '❌'}")
        
        return available
    
    def residues_to_one_hot(self, amino_acid_residues):
        """将氨基酸序列转换为one-hot编码"""
        to_return = []
        normalized_residues = amino_acid_residues.replace('U', 'C').replace('O', 'X')
        
        for char in normalized_residues:
            if char in AMINO_ACID_VOCABULARY:
                to_append = np.zeros(len(AMINO_ACID_VOCABULARY))
                to_append[AMINO_ACID_VOCABULARY.index(char)] = 1.
                to_return.append(to_append)
            elif char == 'B':  # Asparagine or aspartic acid.
                to_append = np.zeros(len(AMINO_ACID_VOCABULARY))
                to_append[AMINO_ACID_VOCABULARY.index('D')] = .5
                to_append[AMINO_ACID_VOCABULARY.index('N')] = .5
                to_return.append(to_append)
            elif char == 'Z':  # Glutamine or glutamic acid.
                to_append = np.zeros(len(AMINO_ACID_VOCABULARY))
                to_append[AMINO_ACID_VOCABULARY.index('E')] = .5
                to_append[AMINO_ACID_VOCABULARY.index('Q')] = .5
                to_return.append(to_append)
            elif char == 'X':
                to_return.append(
                    np.full(len(AMINO_ACID_VOCABULARY), 1. / len(AMINO_ACID_VOCABULARY)))
            elif char == _PFAM_GAP_CHARACTER:
                to_return.append(np.zeros(len(AMINO_ACID_VOCABULARY)))
            else:
                # 对于未知字符，使用X的编码
                logger.warning(f"未知字符 '{char}', 使用X编码")
                to_return.append(
                    np.full(len(AMINO_ACID_VOCABULARY), 1. / len(AMINO_ACID_VOCABULARY)))
        
        return np.array(to_return)
    
    def load_sequences_from_fasta(self, fasta_path, max_length=1000):
        """从FASTA文件加载序列"""
        logger.info(f"从 {fasta_path} 加载序列...")
        sequences = []
        
        try:
            with open(fasta_path, 'r') as f:
                for line in f:
                    if not line.startswith('>'):
                        seq = line.strip()
                        if seq:  # 确保序列不为空
                            sequences.append(seq[:max_length])
            
            logger.info(f"成功加载 {len(sequences)} 个序列")
            return sequences
            
        except FileNotFoundError:
            logger.error(f"文件未找到: {fasta_path}")
            return []
        except Exception as e:
            logger.error(f"加载序列时出错: {e}")
            return []
    
    def extract_cnn_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取CNN特征"""
        if not self.available_extractors["cnn"]:
            logger.warning("CNN模型不可用，跳过CNN特征提取")
            return None
        
        logger.info("正在提取CNN特征...")
        
        try:
            # 这里需要TensorFlow 1.x的代码
            import tensorflow.compat.v1 as tf
            tf.disable_v2_behavior()
            
            if self._cnn_model is None:
                sess = tf.Session()
                graph = tf.Graph()
                with graph.as_default():
                    model_path = "trn-_cnn_random__random_sp_gpu-cnn_for_random_pfam-5356760"
                    saved_model = tf.saved_model.load(sess, ['serve'], model_path)
                
                self._cnn_model = {
                    'session': sess,
                    'graph': graph,
                    'saved_model': saved_model
                }
            
            model_info = self._cnn_model
            sess = model_info['session']
            graph = model_info['graph']
            saved_model = model_info['saved_model']
            
            sequence_input_tensor_name = saved_model.signature_def['confidences'].inputs['sequence'].name
            sequence_lengths_input_tensor_name = saved_model.signature_def['confidences'].inputs['sequence_length'].name
            embedding_signature = saved_model.signature_def['pooled_representation']
            embedding_signature_tensor_name = embedding_signature.outputs['output'].name
            
            features = []
            for i, seq in enumerate(sequences):
                if i % 100 == 0:
                    logger.info(f"处理CNN特征 {i+1}/{len(sequences)}")
                
                with graph.as_default():
                    embedding = sess.run(
                        embedding_signature_tensor_name,
                        {
                            sequence_input_tensor_name: [self.residues_to_one_hot(seq)],
                            sequence_lengths_input_tensor_name: [len(seq)],
                        }
                    )
                features.append(embedding[0])
            
            logger.info(f"CNN特征提取完成: {len(features)} x {len(features[0])}")
            return np.array(features)
            
        except Exception as e:
            logger.error(f"CNN特征提取失败: {e}")
            return None
    
    def extract_protrans_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取ProtTrans特征"""
        logger.info("正在提取ProtTrans特征...")
        
        try:
            if self._protrans_model is None:
                from transformers import T5Tokenizer, T5EncoderModel
                
                logger.info("加载ProtTrans模型...")
                # 尝试使用正确的模型名称
                model_name = "Rostlab/prot_t5_xl_uniref50"
                tokenizer = T5Tokenizer.from_pretrained(model_name, do_lower_case=False)
                model = T5EncoderModel.from_pretrained(model_name)
                
                model = model.to(self.device)
                model = model.eval()
                
                self._protrans_tokenizer = tokenizer
                self._protrans_model = model
            
            tokenizer = self._protrans_tokenizer
            model = self._protrans_model
            
            # 预处理序列（添加空格）
            sequences_spaced = []
            for seq in sequences:
                spaced_seq = ' '.join(list(seq))
                sequences_spaced.append(spaced_seq)
            
            features = []
            for i, seq_spaced in enumerate(sequences_spaced):
                if i % 50 == 0:
                    logger.info(f"处理ProtTrans特征 {i+1}/{len(sequences_spaced)}")
                
                # 替换非标准氨基酸
                seq_spaced = re.sub(r"[UZOB]", "X", seq_spaced)
                
                # 编码
                ids = tokenizer.batch_encode_plus([seq_spaced], add_special_tokens=True, padding=True)
                input_ids = torch.tensor(ids['input_ids']).to(self.device)
                attention_mask = torch.tensor(ids['attention_mask']).to(self.device)
                
                # 提取特征
                with torch.no_grad():
                    embedding = model(input_ids=input_ids, attention_mask=attention_mask)
                
                # 平均池化
                embedding = embedding.last_hidden_state.cpu().numpy()
                seq_len = (attention_mask[0] == 1).sum()
                seq_emb = embedding[0][:seq_len - 1]  # 去除特殊token
                
                # 计算平均值
                feature_vector = np.mean(seq_emb, axis=0)
                features.append(feature_vector)
                
                # 清理GPU内存
                if i % 100 == 0:
                    torch.cuda.empty_cache()
                    gc.collect()
            
            logger.info(f"ProtTrans特征提取完成: {len(features)} x {len(features[0])}")
            return np.array(features)
            
        except Exception as e:
            logger.error(f"ProtTrans特征提取失败: {e}")
            return None
    
    def extract_cpc_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取CPC特征"""
        if not self.available_extractors["cpc"]:
            logger.warning("CPC模型不可用，跳过CPC特征提取")
            return None
        
        logger.info("正在提取CPC特征...")
        
        try:
            # 这里需要实现CPC特征提取
            # 由于CPC模型的具体实现可能需要特定的库，这里提供框架
            logger.warning("CPC特征提取需要具体的模型实现，返回占位特征")
            
            # 返回占位特征（实际使用时需要替换）
            features = np.random.randn(len(sequences), 1536)
            logger.info(f"CPC特征提取完成: {features.shape}")
            return features
            
        except Exception as e:
            logger.error(f"CPC特征提取失败: {e}")
            return None
    
    def extract_elmo_features(self, sequences: List[str]) -> Optional[np.ndarray]:
        """提取ELMO特征"""
        if not self.available_extractors["elmo"]:
            logger.warning("ELMO模型不可用，跳过ELMO特征提取")
            return None
        
        logger.info("正在提取ELMO特征...")
        
        try:
            if self._elmo_model is None:
                from allennlp.modules.elmo import ElmoEmbedder
                
                model_dir = Path("uniref50_v2")
                weights = model_dir / 'weights.hdf5'
                options = model_dir / 'options.json'
                
                embedder = ElmoEmbedder(str(options), str(weights), 
                                      cuda_device=self.gpu_device if self.use_gpu else -1)
                self._elmo_model = embedder
            
            embedder = self._elmo_model
            
            features = []
            for i, seq in enumerate(sequences):
                if i % 100 == 0:
                    logger.info(f"处理ELMO特征 {i+1}/{len(sequences)}")
                
                embedding = embedder.embed_sentence(list(seq))  # List-of-Lists with shape [3,L,1024]
                protein_embd = (torch.tensor(embedding).sum(dim=0).mean(dim=0)).cpu().detach().numpy()
                features.append(protein_embd)
            
            logger.info(f"ELMO特征提取完成: {len(features)} x {len(features[0])}")
            return np.array(features)
            
        except Exception as e:
            logger.error(f"ELMO特征提取失败: {e}")
            return None
    
    def extract_all_features(self, sequences: List[str], extractors: Optional[List[str]] = None) -> Dict[str, np.ndarray]:
        """提取所有可用的特征"""
        if extractors is None:
            extractors = ["protrans"]  # 默认只使用ProtTrans，因为它最稳定
        
        features = {}
        
        for extractor in extractors:
            if extractor == "cnn" and self.available_extractors.get("cnn", False):
                features["cnn"] = self.extract_cnn_features(sequences)
            elif extractor == "protrans":
                features["protrans"] = self.extract_protrans_features(sequences)
            elif extractor == "cpc" and self.available_extractors.get("cpc", False):
                features["cpc"] = self.extract_cpc_features(sequences)
            elif extractor == "elmo" and self.available_extractors.get("elmo", False):
                features["elmo"] = self.extract_elmo_features(sequences)
        
        # 清理None值
        features = {k: v for k, v in features.items() if v is not None}
        
        return features
    
    def save_features(self, features: Dict[str, np.ndarray], output_path: str):
        """保存特征到文件"""
        logger.info(f"保存特征到: {output_path}")
        
        # 转置特征矩阵并创建字典
        feature_dict = {}
        
        for feature_type, feature_matrix in features.items():
            feature_matrix_t = feature_matrix.T
            
            for i in range(len(feature_matrix_t)):
                if feature_type == "cnn":
                    key = f'CNN_{i+1}'
                elif feature_type == "protrans":
                    key = f'protTrans_{i+1}'
                elif feature_type == "cpc":
                    key = f'CPC_{i+1}'
                elif feature_type == "elmo":
                    key = f'ELMO_{i+1}'
                
                feature_dict[key] = feature_matrix_t[i]
        
        # 保存为pickle文件
        with open(output_path, 'wb') as f:
            pickle.dump(feature_dict, f)
        
        logger.info(f"特征保存完成: {len(feature_dict)} 个特征维度")

def main():
    """主函数示例"""
    # 示例序列
    test_sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPACYPDRYKHVYTILNPTKDHGESTCDGAIADLXMLTFVENEYKALVAELEKENEERRRLKDPNKPEHPVLVQISGEEALEELGVIACIGEKLDEREAGITEKVVFEQTKAIADNVKDWSKVVLAYEPVWAIGTGKTATPQQAQEVHEKLRGWLKTHVSDAVAVAQSTRIIYGGSVTGGNCKELASQHDVDGFLVGGASLKPEFVDIINAKQ"
    ]
    
    # 创建特征提取器
    extractor = ImprovedFeatureExtractor()
    
    # 提取特征
    features = extractor.extract_all_features(test_sequences, extractors=["protrans"])
    
    # 保存特征
    if features:
        extractor.save_features(features, "improved_test_features.pkl")
        print("特征提取和保存完成!")
    else:
        print("特征提取失败!")

if __name__ == "__main__":
    main()
